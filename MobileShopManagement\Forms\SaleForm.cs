using MobileShopManagement.BusinessLogic;
using MobileShopManagement.Models;
using MobileShopManagement.Properties;
using System.Configuration;

namespace MobileShopManagement.Forms
{
    /// <summary>
    /// نموذج إضافة/تعديل البيع - Sale add/edit form
    /// Form for adding or editing sales transactions
    /// </summary>
    public partial class SaleForm : Form
    {
        private readonly SalesManager _salesManager;
        private readonly Sale? _existingSale;
        private readonly bool _isEditMode;

        /// <summary>
        /// Constructor for adding new sale
        /// </summary>
        public SaleForm()
        {
            InitializeComponent();
            _salesManager = new SalesManager();
            _isEditMode = false;
            SetupForm();
        }

        /// <summary>
        /// Constructor for editing existing sale
        /// </summary>
        /// <param name="sale">Sale to edit</param>
        public SaleForm(Sale sale)
        {
            InitializeComponent();
            _salesManager = new SalesManager();
            _existingSale = sale;
            _isEditMode = true;
            SetupForm();
            LoadSaleData();
        }

        /// <summary>
        /// Sets up the form with Arabic RTL support and styling
        /// </summary>
        private void SetupForm()
        {
            // Set RTL support
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // Set form properties
            this.Text = _isEditMode ? "تعديل البيع" : Resources.AddSale;
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Size = new Size(500, 450);
            this.BackColor = Color.FromArgb(250, 250, 250);

            // Set font
            var fontName = ConfigurationManager.AppSettings["DefaultFont"] ?? "Tahoma";
            var fontSize = float.TryParse(ConfigurationManager.AppSettings["DefaultFontSize"], out var size) ? size : 9f;
            this.Font = new Font(fontName, fontSize);

            CreateControls();
            SetupEventHandlers();
        }

        /// <summary>
        /// Creates and positions form controls
        /// </summary>
        private void CreateControls()
        {
            var yPos = 20;
            var labelWidth = 120;
            var controlWidth = 300;
            var controlHeight = 25;
            var spacing = 35;

            // Title
            var lblTitle = new Label
            {
                Text = _isEditMode ? "تعديل بيانات البيع" : "إضافة بيع جديد",
                Font = new Font(this.Font.FontFamily, 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(33, 150, 243),
                Location = new Point(20, yPos),
                Size = new Size(440, 30),
                TextAlign = ContentAlignment.MiddleCenter
            };
            this.Controls.Add(lblTitle);
            yPos += 40;

            // Product Name
            var lblProductName = new Label
            {
                Text = Resources.ProductName + " *:",
                Location = new Point(350, yPos),
                Size = new Size(labelWidth, controlHeight),
                TextAlign = ContentAlignment.MiddleRight
            };
            this.Controls.Add(lblProductName);

            txtProductName = new TextBox
            {
                Location = new Point(30, yPos),
                Size = new Size(controlWidth, controlHeight),
                Font = new Font(this.Font.FontFamily, 10),
                TextAlign = HorizontalAlignment.Right
            };
            this.Controls.Add(txtProductName);
            yPos += spacing;

            // Sale Price
            var lblSalePrice = new Label
            {
                Text = Resources.SalePrice + " (ر.س) *:",
                Location = new Point(350, yPos),
                Size = new Size(labelWidth, controlHeight),
                TextAlign = ContentAlignment.MiddleRight
            };
            this.Controls.Add(lblSalePrice);

            txtSalePrice = new TextBox
            {
                Location = new Point(30, yPos),
                Size = new Size(controlWidth, controlHeight),
                Font = new Font(this.Font.FontFamily, 10),
                TextAlign = HorizontalAlignment.Right
            };
            this.Controls.Add(txtSalePrice);
            yPos += spacing;

            // Quantity
            var lblQuantity = new Label
            {
                Text = Resources.Quantity + " *:",
                Location = new Point(350, yPos),
                Size = new Size(labelWidth, controlHeight),
                TextAlign = ContentAlignment.MiddleRight
            };
            this.Controls.Add(lblQuantity);

            nudQuantity = new NumericUpDown
            {
                Location = new Point(30, yPos),
                Size = new Size(controlWidth, controlHeight),
                Font = new Font(this.Font.FontFamily, 10),
                Minimum = 1,
                Maximum = 1000,
                Value = 1,
                TextAlign = HorizontalAlignment.Right
            };
            this.Controls.Add(nudQuantity);
            yPos += spacing;

            // Sale Date
            var lblSaleDate = new Label
            {
                Text = Resources.SaleDate + " *:",
                Location = new Point(350, yPos),
                Size = new Size(labelWidth, controlHeight),
                TextAlign = ContentAlignment.MiddleRight
            };
            this.Controls.Add(lblSaleDate);

            dtpSaleDate = new DateTimePicker
            {
                Location = new Point(30, yPos),
                Size = new Size(controlWidth, controlHeight),
                Font = new Font(this.Font.FontFamily, 10),
                Format = DateTimePickerFormat.Custom,
                CustomFormat = "yyyy/MM/dd HH:mm",
                ShowUpDown = false,
                Value = DateTime.Now
            };
            this.Controls.Add(dtpSaleDate);
            yPos += spacing;

            // Customer Name
            var lblCustomerName = new Label
            {
                Text = Resources.CustomerName + ":",
                Location = new Point(350, yPos),
                Size = new Size(labelWidth, controlHeight),
                TextAlign = ContentAlignment.MiddleRight
            };
            this.Controls.Add(lblCustomerName);

            txtCustomerName = new TextBox
            {
                Location = new Point(30, yPos),
                Size = new Size(controlWidth, controlHeight),
                Font = new Font(this.Font.FontFamily, 10),
                TextAlign = HorizontalAlignment.Right
            };
            this.Controls.Add(txtCustomerName);
            yPos += spacing;

            // Customer Phone
            var lblCustomerPhone = new Label
            {
                Text = Resources.CustomerPhone + ":",
                Location = new Point(350, yPos),
                Size = new Size(labelWidth, controlHeight),
                TextAlign = ContentAlignment.MiddleRight
            };
            this.Controls.Add(lblCustomerPhone);

            txtCustomerPhone = new TextBox
            {
                Location = new Point(30, yPos),
                Size = new Size(controlWidth, controlHeight),
                Font = new Font(this.Font.FontFamily, 10),
                TextAlign = HorizontalAlignment.Right,
                PlaceholderText = "مثال: 0501234567"
            };
            this.Controls.Add(txtCustomerPhone);
            yPos += spacing;

            // Notes
            var lblNotes = new Label
            {
                Text = Resources.Notes + ":",
                Location = new Point(350, yPos),
                Size = new Size(labelWidth, controlHeight),
                TextAlign = ContentAlignment.MiddleRight
            };
            this.Controls.Add(lblNotes);

            txtNotes = new TextBox
            {
                Location = new Point(30, yPos),
                Size = new Size(controlWidth, 50),
                Font = new Font(this.Font.FontFamily, 10),
                TextAlign = HorizontalAlignment.Right,
                Multiline = true,
                ScrollBars = ScrollBars.Vertical
            };
            this.Controls.Add(txtNotes);
            yPos += 65;

            // Total Amount (Read-only)
            var lblTotalAmount = new Label
            {
                Text = Resources.TotalAmount + ":",
                Location = new Point(350, yPos),
                Size = new Size(labelWidth, controlHeight),
                TextAlign = ContentAlignment.MiddleRight,
                Font = new Font(this.Font.FontFamily, 10, FontStyle.Bold)
            };
            this.Controls.Add(lblTotalAmount);

            lblTotalAmountValue = new Label
            {
                Text = "0.00 ر.س",
                Location = new Point(30, yPos),
                Size = new Size(controlWidth, controlHeight),
                TextAlign = ContentAlignment.MiddleRight,
                Font = new Font(this.Font.FontFamily, 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(76, 175, 80),
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = Color.FromArgb(245, 245, 245)
            };
            this.Controls.Add(lblTotalAmountValue);
            yPos += 45;

            // Buttons
            btnSave = new Button
            {
                Text = Resources.Save,
                Location = new Point(250, yPos),
                Size = new Size(100, 35),
                BackColor = Color.FromArgb(76, 175, 80),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font(this.Font.FontFamily, 10, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            btnSave.FlatAppearance.BorderSize = 0;
            this.Controls.Add(btnSave);

            btnCancel = new Button
            {
                Text = Resources.Cancel,
                Location = new Point(130, yPos),
                Size = new Size(100, 35),
                BackColor = Color.FromArgb(158, 158, 158),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font(this.Font.FontFamily, 10, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            btnCancel.FlatAppearance.BorderSize = 0;
            this.Controls.Add(btnCancel);

            // Required field indicator
            var lblRequired = new Label
            {
                Text = "* الحقول المطلوبة",
                Location = new Point(30, yPos + 45),
                Size = new Size(200, 20),
                ForeColor = Color.Red,
                Font = new Font(this.Font.FontFamily, 8)
            };
            this.Controls.Add(lblRequired);
        }

        /// <summary>
        /// Sets up event handlers
        /// </summary>
        private void SetupEventHandlers()
        {
            btnSave.Click += BtnSave_Click;
            btnCancel.Click += BtnCancel_Click;
            
            // Calculate total when price or quantity changes
            txtSalePrice.TextChanged += CalculateTotal;
            nudQuantity.ValueChanged += CalculateTotal;
            
            // Validation events
            txtSalePrice.KeyPress += TxtSalePrice_KeyPress;
            txtCustomerPhone.KeyPress += TxtCustomerPhone_KeyPress;
            
            // Set tab order
            txtProductName.TabIndex = 0;
            txtSalePrice.TabIndex = 1;
            nudQuantity.TabIndex = 2;
            dtpSaleDate.TabIndex = 3;
            txtCustomerName.TabIndex = 4;
            txtCustomerPhone.TabIndex = 5;
            txtNotes.TabIndex = 6;
            btnSave.TabIndex = 7;
            btnCancel.TabIndex = 8;

            this.AcceptButton = btnSave;
            this.CancelButton = btnCancel;
        }

        /// <summary>
        /// Loads existing sale data for editing
        /// </summary>
        private void LoadSaleData()
        {
            if (_existingSale != null)
            {
                txtProductName.Text = _existingSale.ProductName;
                txtSalePrice.Text = _existingSale.SalePrice.ToString("F2");
                nudQuantity.Value = _existingSale.QuantitySold;
                dtpSaleDate.Value = _existingSale.SaleDate;
                txtCustomerName.Text = _existingSale.CustomerName ?? "";
                txtCustomerPhone.Text = _existingSale.CustomerPhone ?? "";
                txtNotes.Text = _existingSale.Notes ?? "";
                
                CalculateTotal(null, EventArgs.Empty);
            }
        }

        /// <summary>
        /// Calculates and displays total amount
        /// </summary>
        private void CalculateTotal(object? sender, EventArgs e)
        {
            if (decimal.TryParse(txtSalePrice.Text, out var price))
            {
                var total = price * nudQuantity.Value;
                lblTotalAmountValue.Text = $"{total:F2} ر.س";
            }
            else
            {
                lblTotalAmountValue.Text = "0.00 ر.س";
            }
        }

        /// <summary>
        /// Validates numeric input for price
        /// </summary>
        private void TxtSalePrice_KeyPress(object? sender, KeyPressEventArgs e)
        {
            if (!char.IsControl(e.KeyChar) && !char.IsDigit(e.KeyChar) && e.KeyChar != '.')
            {
                e.Handled = true;
            }

            // Only allow one decimal point
            if (e.KeyChar == '.' && txtSalePrice.Text.Contains('.'))
            {
                e.Handled = true;
            }
        }

        /// <summary>
        /// Validates phone number input
        /// </summary>
        private void TxtCustomerPhone_KeyPress(object? sender, KeyPressEventArgs e)
        {
            if (!char.IsControl(e.KeyChar) && !char.IsDigit(e.KeyChar) && 
                e.KeyChar != '+' && e.KeyChar != '-' && e.KeyChar != '(' && 
                e.KeyChar != ')' && e.KeyChar != ' ')
            {
                e.Handled = true;
            }
        }

        /// <summary>
        /// Save button click event handler
        /// </summary>
        private async void BtnSave_Click(object? sender, EventArgs e)
        {
            try
            {
                // Validate input
                if (!ValidateInput())
                    return;

                // Create sale object
                var sale = CreateSaleFromInput();

                // Show loading
                btnSave.Enabled = false;
                btnSave.Text = "جاري الحفظ...";
                this.Cursor = Cursors.WaitCursor;

                if (_isEditMode && _existingSale != null)
                {
                    // Update existing sale
                    sale.SaleID = _existingSale.SaleID;
                    var success = await _salesManager.UpdateSaleAsync(sale, SessionManager.CurrentUsername);
                    
                    if (success)
                    {
                        MessageBox.Show(Resources.SaleUpdated, Resources.Success,
                            MessageBoxButtons.OK, MessageBoxIcon.Information,
                            MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
                        
                        this.DialogResult = DialogResult.OK;
                        this.Close();
                    }
                    else
                    {
                        MessageBox.Show("فشل في تحديث البيع", Resources.Error,
                            MessageBoxButtons.OK, MessageBoxIcon.Error,
                            MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
                    }
                }
                else
                {
                    // Create new sale
                    var saleId = await _salesManager.CreateSaleAsync(sale);
                    
                    MessageBox.Show(Resources.SaleAdded, Resources.Success,
                        MessageBoxButtons.OK, MessageBoxIcon.Information,
                        MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
                    
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ البيع: {ex.Message}", Resources.Error,
                    MessageBoxButtons.OK, MessageBoxIcon.Error,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
            }
            finally
            {
                btnSave.Enabled = true;
                btnSave.Text = Resources.Save;
                this.Cursor = Cursors.Default;
            }
        }

        /// <summary>
        /// Cancel button click event handler
        /// </summary>
        private void BtnCancel_Click(object? sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        /// <summary>
        /// Validates form input
        /// </summary>
        /// <returns>True if input is valid</returns>
        private bool ValidateInput()
        {
            var errors = new List<string>();

            // Product name validation
            if (string.IsNullOrWhiteSpace(txtProductName.Text))
                errors.Add("اسم المنتج مطلوب");

            // Price validation
            if (!decimal.TryParse(txtSalePrice.Text, out var price) || price <= 0)
                errors.Add("سعر البيع يجب أن يكون رقماً أكبر من صفر");

            // Quantity validation
            if (nudQuantity.Value <= 0)
                errors.Add("الكمية يجب أن تكون أكبر من صفر");

            // Phone validation (if provided)
            if (!string.IsNullOrWhiteSpace(txtCustomerPhone.Text))
            {
                var phone = txtCustomerPhone.Text.Trim();
                if (phone.Length < 10 || phone.Length > 15)
                    errors.Add("رقم الهاتف غير صحيح");
            }

            if (errors.Any())
            {
                var errorMessage = string.Join("\n", errors);
                MessageBox.Show(errorMessage, "بيانات غير صحيحة",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
                return false;
            }

            return true;
        }

        /// <summary>
        /// Creates Sale object from form input
        /// </summary>
        /// <returns>Sale object</returns>
        private Sale CreateSaleFromInput()
        {
            return new Sale
            {
                ProductName = txtProductName.Text.Trim(),
                SalePrice = decimal.Parse(txtSalePrice.Text),
                QuantitySold = (int)nudQuantity.Value,
                SaleDate = dtpSaleDate.Value,
                CustomerName = string.IsNullOrWhiteSpace(txtCustomerName.Text) ? null : txtCustomerName.Text.Trim(),
                CustomerPhone = string.IsNullOrWhiteSpace(txtCustomerPhone.Text) ? null : txtCustomerPhone.Text.Trim(),
                Notes = string.IsNullOrWhiteSpace(txtNotes.Text) ? null : txtNotes.Text.Trim(),
                CreatedBy = SessionManager.CurrentUsername
            };
        }

        #region Form Controls
        private TextBox txtProductName = null!;
        private TextBox txtSalePrice = null!;
        private NumericUpDown nudQuantity = null!;
        private DateTimePicker dtpSaleDate = null!;
        private TextBox txtCustomerName = null!;
        private TextBox txtCustomerPhone = null!;
        private TextBox txtNotes = null!;
        private Label lblTotalAmountValue = null!;
        private Button btnSave = null!;
        private Button btnCancel = null!;
        #endregion
    }
}
