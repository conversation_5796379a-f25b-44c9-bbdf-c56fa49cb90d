using MobileShopManagement.BusinessLogic;
using MobileShopManagement.Properties;
using System.Configuration;

namespace MobileShopManagement.Forms
{
    /// <summary>
    /// نموذج التقرير اليومي - Daily report form
    /// Form for viewing daily sales reports and statistics
    /// </summary>
    public partial class DailyReportForm : Form
    {
        private readonly SalesManager _salesManager;

        /// <summary>
        /// Constructor
        /// </summary>
        public DailyReportForm()
        {
            InitializeComponent();
            _salesManager = new SalesManager();
            SetupForm();
        }

        /// <summary>
        /// Sets up the form with Arabic RTL support and styling
        /// </summary>
        private void SetupForm()
        {
            // Set RTL support
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // Set form properties
            this.Text = "التقرير اليومي";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.BackColor = Color.FromArgb(250, 250, 250);

            // Set font
            var fontName = ConfigurationManager.AppSettings["DefaultFont"] ?? "Tahoma";
            var fontSize = float.TryParse(ConfigurationManager.AppSettings["DefaultFontSize"], out var size) ? size : 9f;
            this.Font = new Font(fontName, fontSize);

            CreateControls();
            SetupEventHandlers();
            LoadTodayReport();
        }

        /// <summary>
        /// Creates and positions form controls
        /// </summary>
        private void CreateControls()
        {
            // Title panel
            var titlePanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 80,
                BackColor = Color.FromArgb(63, 81, 181),
                Padding = new Padding(20)
            };

            var lblTitle = new Label
            {
                Text = "التقرير اليومي للمبيعات",
                Font = new Font(this.Font.FontFamily, 16, FontStyle.Bold),
                ForeColor = Color.White,
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter
            };
            titlePanel.Controls.Add(lblTitle);
            this.Controls.Add(titlePanel);

            // Date selection panel
            var datePanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 60,
                BackColor = Color.FromArgb(240, 240, 240),
                Padding = new Padding(20, 15, 20, 15)
            };

            var lblDate = new Label
            {
                Text = "اختر التاريخ:",
                Location = new Point(650, 20),
                Size = new Size(80, 25),
                TextAlign = ContentAlignment.MiddleRight,
                Font = new Font(this.Font.FontFamily, 10, FontStyle.Bold)
            };
            datePanel.Controls.Add(lblDate);

            dtpReportDate = new DateTimePicker
            {
                Location = new Point(450, 20),
                Size = new Size(180, 25),
                Format = DateTimePickerFormat.Long,
                Value = DateTime.Today
            };
            datePanel.Controls.Add(dtpReportDate);

            btnLoadReport = new Button
            {
                Text = "تحميل التقرير",
                Location = new Point(320, 18),
                Size = new Size(120, 30),
                BackColor = Color.FromArgb(76, 175, 80),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font(this.Font.FontFamily, 9, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            btnLoadReport.FlatAppearance.BorderSize = 0;
            datePanel.Controls.Add(btnLoadReport);

            this.Controls.Add(datePanel);

            // Main content panel
            var contentPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20),
                AutoScroll = true
            };

            // Summary section
            CreateSummarySection(contentPanel);

            // Top products section
            CreateTopProductsSection(contentPanel);

            this.Controls.Add(contentPanel);

            // Buttons panel
            CreateButtonsPanel();
        }

        /// <summary>
        /// Creates the summary section
        /// </summary>
        private void CreateSummarySection(Panel parent)
        {
            var summaryPanel = new Panel
            {
                Location = new Point(20, 20),
                Size = new Size(720, 200),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            var lblSummaryTitle = new Label
            {
                Text = "ملخص المبيعات",
                Font = new Font(this.Font.FontFamily, 14, FontStyle.Bold),
                ForeColor = Color.FromArgb(33, 150, 243),
                Location = new Point(10, 10),
                Size = new Size(700, 30),
                TextAlign = ContentAlignment.MiddleCenter
            };
            summaryPanel.Controls.Add(lblSummaryTitle);

            // Create summary cards
            CreateSummaryCard(summaryPanel, "عدد المعاملات", "0", new Point(20, 50), Color.FromArgb(76, 175, 80), ref lblTotalTransactions);
            CreateSummaryCard(summaryPanel, "إجمالي المبيعات", "0.00 ر.س", new Point(190, 50), Color.FromArgb(33, 150, 243), ref lblTotalRevenue);
            CreateSummaryCard(summaryPanel, "متوسط المعاملة", "0.00 ر.س", new Point(360, 50), Color.FromArgb(255, 152, 0), ref lblAverageTransaction);
            CreateSummaryCard(summaryPanel, "عدد العملاء", "0", new Point(530, 50), Color.FromArgb(156, 39, 176), ref lblUniqueCustomers);

            parent.Controls.Add(summaryPanel);
        }

        /// <summary>
        /// Creates a summary card
        /// </summary>
        private void CreateSummaryCard(Panel parent, string title, string value, Point location, Color color, ref Label valueLabel)
        {
            var cardPanel = new Panel
            {
                Location = location,
                Size = new Size(150, 120),
                BackColor = color
            };

            var titleLabel = new Label
            {
                Text = title,
                Font = new Font(this.Font.FontFamily, 9, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(10, 15),
                Size = new Size(130, 40),
                TextAlign = ContentAlignment.MiddleCenter
            };
            cardPanel.Controls.Add(titleLabel);

            valueLabel = new Label
            {
                Text = value,
                Font = new Font(this.Font.FontFamily, 12, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(10, 60),
                Size = new Size(130, 40),
                TextAlign = ContentAlignment.MiddleCenter
            };
            cardPanel.Controls.Add(valueLabel);

            parent.Controls.Add(cardPanel);
        }

        /// <summary>
        /// Creates the top products section
        /// </summary>
        private void CreateTopProductsSection(Panel parent)
        {
            var productsPanel = new Panel
            {
                Location = new Point(20, 240),
                Size = new Size(720, 250),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            var lblProductsTitle = new Label
            {
                Text = "أفضل 5 منتجات مبيعاً",
                Font = new Font(this.Font.FontFamily, 14, FontStyle.Bold),
                ForeColor = Color.FromArgb(33, 150, 243),
                Location = new Point(10, 10),
                Size = new Size(700, 30),
                TextAlign = ContentAlignment.MiddleCenter
            };
            productsPanel.Controls.Add(lblProductsTitle);

            dgvTopProducts = new DataGridView
            {
                Location = new Point(20, 50),
                Size = new Size(680, 180),
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal,
                ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.None,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                ReadOnly = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                AllowUserToResizeRows = false,
                RowHeadersVisible = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                Font = new Font(this.Font.FontFamily, 9)
            };

            // Style headers
            dgvTopProducts.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(63, 81, 181);
            dgvTopProducts.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvTopProducts.ColumnHeadersDefaultCellStyle.Font = new Font(this.Font.FontFamily, 10, FontStyle.Bold);
            dgvTopProducts.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgvTopProducts.ColumnHeadersHeight = 35;

            // Style rows
            dgvTopProducts.DefaultCellStyle.BackColor = Color.White;
            dgvTopProducts.DefaultCellStyle.ForeColor = Color.Black;
            dgvTopProducts.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgvTopProducts.RowTemplate.Height = 30;

            SetupTopProductsColumns();
            productsPanel.Controls.Add(dgvTopProducts);
            parent.Controls.Add(productsPanel);
        }

        /// <summary>
        /// Sets up top products data grid columns
        /// </summary>
        private void SetupTopProductsColumns()
        {
            dgvTopProducts.Columns.Clear();

            dgvTopProducts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "ProductName",
                HeaderText = "اسم المنتج",
                DataPropertyName = "ProductName",
                DefaultCellStyle = { Alignment = DataGridViewContentAlignment.MiddleRight }
            });

            dgvTopProducts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "TotalQuantity",
                HeaderText = "الكمية المباعة",
                DataPropertyName = "TotalQuantity",
                DefaultCellStyle = { Alignment = DataGridViewContentAlignment.MiddleCenter }
            });

            dgvTopProducts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "TotalRevenue",
                HeaderText = "إجمالي المبيعات",
                DataPropertyName = "TotalRevenueFormatted",
                DefaultCellStyle = { Alignment = DataGridViewContentAlignment.MiddleCenter }
            });

            dgvTopProducts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "TransactionCount",
                HeaderText = "عدد المعاملات",
                DataPropertyName = "TransactionCount",
                DefaultCellStyle = { Alignment = DataGridViewContentAlignment.MiddleCenter }
            });
        }

        /// <summary>
        /// Creates the buttons panel
        /// </summary>
        private void CreateButtonsPanel()
        {
            var buttonsPanel = new Panel
            {
                Dock = DockStyle.Bottom,
                Height = 60,
                BackColor = Color.FromArgb(240, 240, 240),
                Padding = new Padding(20, 15, 20, 15)
            };

            btnPrint = new Button
            {
                Text = Resources.Print,
                Location = new Point(20, 15),
                Size = new Size(100, 30),
                BackColor = Color.FromArgb(103, 58, 183),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font(this.Font.FontFamily, 9, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            btnPrint.FlatAppearance.BorderSize = 0;
            buttonsPanel.Controls.Add(btnPrint);

            btnExport = new Button
            {
                Text = Resources.Export,
                Location = new Point(130, 15),
                Size = new Size(100, 30),
                BackColor = Color.FromArgb(33, 150, 243),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font(this.Font.FontFamily, 9, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            btnExport.FlatAppearance.BorderSize = 0;
            buttonsPanel.Controls.Add(btnExport);

            btnClose = new Button
            {
                Text = Resources.Close,
                Location = new Point(650, 15),
                Size = new Size(100, 30),
                BackColor = Color.FromArgb(158, 158, 158),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font(this.Font.FontFamily, 9, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            btnClose.FlatAppearance.BorderSize = 0;
            buttonsPanel.Controls.Add(btnClose);

            this.Controls.Add(buttonsPanel);
        }

        /// <summary>
        /// Sets up event handlers
        /// </summary>
        private void SetupEventHandlers()
        {
            btnLoadReport.Click += BtnLoadReport_Click;
            btnPrint.Click += BtnPrint_Click;
            btnExport.Click += BtnExport_Click;
            btnClose.Click += BtnClose_Click;
            dtpReportDate.ValueChanged += DtpReportDate_ValueChanged;
        }

        /// <summary>
        /// Loads today's report by default
        /// </summary>
        private void LoadTodayReport()
        {
            LoadDailyReport(DateTime.Today);
        }

        /// <summary>
        /// Loads daily report for specified date
        /// </summary>
        private async void LoadDailyReport(DateTime date)
        {
            try
            {
                btnLoadReport.Enabled = false;
                btnLoadReport.Text = "جاري التحميل...";

                var summary = await _salesManager.GetDailySalesSummaryAsync(date);

                // Update summary cards
                lblTotalTransactions.Text = summary.TotalTransactions.ToString();
                lblTotalRevenue.Text = summary.TotalRevenueFormatted;
                lblAverageTransaction.Text = summary.AverageTransactionFormatted;
                lblUniqueCustomers.Text = summary.UniqueCustomers.ToString();

                // Update top products grid
                dgvTopProducts.DataSource = summary.TopProducts;

                // Update form title with date
                this.Text = $"التقرير اليومي - {summary.DateArabic}";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل التقرير: {ex.Message}", Resources.Error,
                    MessageBoxButtons.OK, MessageBoxIcon.Error,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
            }
            finally
            {
                btnLoadReport.Enabled = true;
                btnLoadReport.Text = "تحميل التقرير";
            }
        }

        #region Event Handlers

        private void BtnLoadReport_Click(object? sender, EventArgs e)
        {
            LoadDailyReport(dtpReportDate.Value.Date);
        }

        private void DtpReportDate_ValueChanged(object? sender, EventArgs e)
        {
            LoadDailyReport(dtpReportDate.Value.Date);
        }

        private void BtnPrint_Click(object? sender, EventArgs e)
        {
            // TODO: Implement print functionality
            MessageBox.Show("سيتم تنفيذ وظيفة الطباعة قريباً", "قيد التطوير",
                MessageBoxButtons.OK, MessageBoxIcon.Information,
                MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
        }

        private void BtnExport_Click(object? sender, EventArgs e)
        {
            // TODO: Implement export functionality
            MessageBox.Show("سيتم تنفيذ وظيفة التصدير قريباً", "قيد التطوير",
                MessageBoxButtons.OK, MessageBoxIcon.Information,
                MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
        }

        private void BtnClose_Click(object? sender, EventArgs e)
        {
            this.Close();
        }

        #endregion

        #region Form Controls
        private DateTimePicker dtpReportDate = null!;
        private Button btnLoadReport = null!;
        private Label lblTotalTransactions = null!;
        private Label lblTotalRevenue = null!;
        private Label lblAverageTransaction = null!;
        private Label lblUniqueCustomers = null!;
        private DataGridView dgvTopProducts = null!;
        private Button btnPrint = null!;
        private Button btnExport = null!;
        private Button btnClose = null!;
        #endregion
    }
}
