using MobileShopManagement.BusinessLogic;
using MobileShopManagement.DataAccess;
using MobileShopManagement.Properties;
using System.Configuration;

namespace MobileShopManagement.Forms
{
    /// <summary>
    /// نموذج النسخ الاحتياطي - Backup form
    /// Form for creating database backups (Admin only)
    /// </summary>
    public partial class BackupForm : Form
    {
        private readonly string _defaultBackupDirectory;

        /// <summary>
        /// Constructor
        /// </summary>
        public BackupForm()
        {
            InitializeComponent();
            _defaultBackupDirectory = ConfigurationManager.AppSettings["BackupDirectory"] ?? @"C:\MobileShopBackups";
            SetupForm();
        }

        /// <summary>
        /// Sets up the form with Arabic RTL support and styling
        /// </summary>
        private void SetupForm()
        {
            // Check admin permission
            if (!SessionManager.IsAdmin)
            {
                MessageBox.Show("ليس لديك صلاحية لإنشاء نسخة احتياطية", "غير مصرح",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
                this.Close();
                return;
            }

            // Set RTL support
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // Set form properties
            this.Text = "إنشاء نسخة احتياطية";
            this.Size = new Size(600, 450);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.BackColor = Color.FromArgb(250, 250, 250);

            // Set font
            var fontName = ConfigurationManager.AppSettings["DefaultFont"] ?? "Tahoma";
            var fontSize = float.TryParse(ConfigurationManager.AppSettings["DefaultFontSize"], out var size) ? size : 9f;
            this.Font = new Font(fontName, fontSize);

            CreateControls();
            SetupEventHandlers();
            InitializeBackupSettings();
        }

        /// <summary>
        /// Creates and positions form controls
        /// </summary>
        private void CreateControls()
        {
            // Title panel
            var titlePanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 80,
                BackColor = Color.FromArgb(63, 81, 181),
                Padding = new Padding(20)
            };

            var lblTitle = new Label
            {
                Text = "إنشاء نسخة احتياطية من قاعدة البيانات",
                Font = new Font(this.Font.FontFamily, 16, FontStyle.Bold),
                ForeColor = Color.White,
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter
            };
            titlePanel.Controls.Add(lblTitle);
            this.Controls.Add(titlePanel);

            // Main content panel
            var contentPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(30, 20, 30, 20)
            };

            var yPos = 20;
            var labelWidth = 150;
            var controlWidth = 400;
            var spacing = 40;

            // Backup location section
            var lblLocationTitle = new Label
            {
                Text = "موقع النسخة الاحتياطية",
                Font = new Font(this.Font.FontFamily, 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(33, 150, 243),
                Location = new Point(30, yPos),
                Size = new Size(500, 25)
            };
            contentPanel.Controls.Add(lblLocationTitle);
            yPos += 35;

            // Backup directory
            var lblBackupPath = new Label
            {
                Text = "مجلد الحفظ:",
                Location = new Point(450, yPos),
                Size = new Size(labelWidth, 25),
                TextAlign = ContentAlignment.MiddleRight
            };
            contentPanel.Controls.Add(lblBackupPath);

            txtBackupPath = new TextBox
            {
                Location = new Point(80, yPos),
                Size = new Size(controlWidth - 50, 25),
                Font = new Font(this.Font.FontFamily, 9),
                TextAlign = HorizontalAlignment.Right,
                ReadOnly = true
            };
            contentPanel.Controls.Add(txtBackupPath);

            btnBrowse = new Button
            {
                Text = "تصفح",
                Location = new Point(30, yPos),
                Size = new Size(45, 25),
                BackColor = Color.FromArgb(158, 158, 158),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font(this.Font.FontFamily, 8),
                Cursor = Cursors.Hand
            };
            btnBrowse.FlatAppearance.BorderSize = 0;
            contentPanel.Controls.Add(btnBrowse);
            yPos += spacing;

            // Backup filename
            var lblBackupName = new Label
            {
                Text = "اسم الملف:",
                Location = new Point(450, yPos),
                Size = new Size(labelWidth, 25),
                TextAlign = ContentAlignment.MiddleRight
            };
            contentPanel.Controls.Add(lblBackupName);

            txtBackupName = new TextBox
            {
                Location = new Point(30, yPos),
                Size = new Size(controlWidth, 25),
                Font = new Font(this.Font.FontFamily, 9),
                TextAlign = HorizontalAlignment.Right
            };
            contentPanel.Controls.Add(txtBackupName);
            yPos += spacing;

            // Backup options section
            var lblOptionsTitle = new Label
            {
                Text = "خيارات النسخ الاحتياطي",
                Font = new Font(this.Font.FontFamily, 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(33, 150, 243),
                Location = new Point(30, yPos),
                Size = new Size(500, 25)
            };
            contentPanel.Controls.Add(lblOptionsTitle);
            yPos += 35;

            // Include system settings
            chkIncludeSettings = new CheckBox
            {
                Text = "تضمين إعدادات النظام",
                Location = new Point(30, yPos),
                Size = new Size(400, 25),
                Checked = true,
                TextAlign = ContentAlignment.MiddleRight,
                Font = new Font(this.Font.FontFamily, 10)
            };
            contentPanel.Controls.Add(chkIncludeSettings);
            yPos += 30;

            // Include audit logs
            chkIncludeAuditLogs = new CheckBox
            {
                Text = "تضمين سجلات المراجعة",
                Location = new Point(30, yPos),
                Size = new Size(400, 25),
                Checked = true,
                TextAlign = ContentAlignment.MiddleRight,
                Font = new Font(this.Font.FontFamily, 10)
            };
            contentPanel.Controls.Add(chkIncludeAuditLogs);
            yPos += 30;

            // Compress backup
            chkCompressBackup = new CheckBox
            {
                Text = "ضغط النسخة الاحتياطية",
                Location = new Point(30, yPos),
                Size = new Size(400, 25),
                Checked = false,
                TextAlign = ContentAlignment.MiddleRight,
                Font = new Font(this.Font.FontFamily, 10)
            };
            contentPanel.Controls.Add(chkCompressBackup);
            yPos += 40;

            // Progress section
            var lblProgressTitle = new Label
            {
                Text = "حالة العملية",
                Font = new Font(this.Font.FontFamily, 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(33, 150, 243),
                Location = new Point(30, yPos),
                Size = new Size(500, 25)
            };
            contentPanel.Controls.Add(lblProgressTitle);
            yPos += 35;

            // Progress bar
            progressBar = new ProgressBar
            {
                Location = new Point(30, yPos),
                Size = new Size(controlWidth, 25),
                Style = ProgressBarStyle.Continuous,
                Minimum = 0,
                Maximum = 100,
                Value = 0
            };
            contentPanel.Controls.Add(progressBar);
            yPos += 35;

            // Status label
            lblStatus = new Label
            {
                Text = "جاهز لإنشاء النسخة الاحتياطية",
                Location = new Point(30, yPos),
                Size = new Size(500, 25),
                TextAlign = ContentAlignment.MiddleCenter,
                Font = new Font(this.Font.FontFamily, 10),
                ForeColor = Color.FromArgb(76, 175, 80)
            };
            contentPanel.Controls.Add(lblStatus);

            this.Controls.Add(contentPanel);

            // Buttons panel
            CreateButtonsPanel();
        }

        /// <summary>
        /// Creates the buttons panel
        /// </summary>
        private void CreateButtonsPanel()
        {
            var buttonsPanel = new Panel
            {
                Dock = DockStyle.Bottom,
                Height = 60,
                BackColor = Color.FromArgb(240, 240, 240),
                Padding = new Padding(30, 15, 30, 15)
            };

            btnCreateBackup = new Button
            {
                Text = "إنشاء النسخة الاحتياطية",
                Location = new Point(30, 15),
                Size = new Size(150, 30),
                BackColor = Color.FromArgb(76, 175, 80),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font(this.Font.FontFamily, 10, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            btnCreateBackup.FlatAppearance.BorderSize = 0;
            buttonsPanel.Controls.Add(btnCreateBackup);

            btnOpenBackupFolder = new Button
            {
                Text = "فتح مجلد النسخ",
                Location = new Point(190, 15),
                Size = new Size(120, 30),
                BackColor = Color.FromArgb(33, 150, 243),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font(this.Font.FontFamily, 9, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            btnOpenBackupFolder.FlatAppearance.BorderSize = 0;
            buttonsPanel.Controls.Add(btnOpenBackupFolder);

            btnClose = new Button
            {
                Text = Resources.Close,
                Location = new Point(450, 15),
                Size = new Size(100, 30),
                BackColor = Color.FromArgb(158, 158, 158),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font(this.Font.FontFamily, 9, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            btnClose.FlatAppearance.BorderSize = 0;
            buttonsPanel.Controls.Add(btnClose);

            this.Controls.Add(buttonsPanel);
        }

        /// <summary>
        /// Sets up event handlers
        /// </summary>
        private void SetupEventHandlers()
        {
            btnBrowse.Click += BtnBrowse_Click;
            btnCreateBackup.Click += BtnCreateBackup_Click;
            btnOpenBackupFolder.Click += BtnOpenBackupFolder_Click;
            btnClose.Click += BtnClose_Click;
        }

        /// <summary>
        /// Initializes backup settings
        /// </summary>
        private void InitializeBackupSettings()
        {
            // Ensure backup directory exists
            if (!Directory.Exists(_defaultBackupDirectory))
            {
                try
                {
                    Directory.CreateDirectory(_defaultBackupDirectory);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"فشل في إنشاء مجلد النسخ الاحتياطي: {ex.Message}", Resources.Error,
                        MessageBoxButtons.OK, MessageBoxIcon.Error,
                        MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
                }
            }

            txtBackupPath.Text = _defaultBackupDirectory;
            
            // Generate default backup filename
            var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            txtBackupName.Text = $"MobileShopDB_Backup_{timestamp}.bak";
        }

        #region Event Handlers

        private void BtnBrowse_Click(object? sender, EventArgs e)
        {
            using var folderDialog = new FolderBrowserDialog
            {
                Description = "اختر مجلد حفظ النسخة الاحتياطية",
                SelectedPath = txtBackupPath.Text,
                ShowNewFolderButton = true
            };

            if (folderDialog.ShowDialog() == DialogResult.OK)
            {
                txtBackupPath.Text = folderDialog.SelectedPath;
            }
        }

        private async void BtnCreateBackup_Click(object? sender, EventArgs e)
        {
            try
            {
                // Validate input
                if (string.IsNullOrWhiteSpace(txtBackupPath.Text) || string.IsNullOrWhiteSpace(txtBackupName.Text))
                {
                    MessageBox.Show("يرجى تحديد مجلد الحفظ واسم الملف", "بيانات ناقصة",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning,
                        MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
                    return;
                }

                // Ensure directory exists
                if (!Directory.Exists(txtBackupPath.Text))
                {
                    Directory.CreateDirectory(txtBackupPath.Text);
                }

                // Prepare backup
                var backupFilePath = Path.Combine(txtBackupPath.Text, txtBackupName.Text);
                
                // Check if file already exists
                if (File.Exists(backupFilePath))
                {
                    var result = MessageBox.Show("الملف موجود بالفعل. هل تريد استبداله؟", "تأكيد الاستبدال",
                        MessageBoxButtons.YesNo, MessageBoxIcon.Question,
                        MessageBoxDefaultButton.Button2, MessageBoxOptions.RtlReading);
                    
                    if (result == DialogResult.No)
                        return;
                }

                // Start backup process
                btnCreateBackup.Enabled = false;
                btnClose.Enabled = false;
                progressBar.Value = 0;
                lblStatus.Text = "جاري إنشاء النسخة الاحتياطية...";
                lblStatus.ForeColor = Color.FromArgb(255, 152, 0);

                // Simulate progress updates
                var progressTimer = new System.Timers.Timer(100);
                progressTimer.Elapsed += (s, e) =>
                {
                    this.Invoke(() =>
                    {
                        if (progressBar.Value < 90)
                        {
                            progressBar.Value += 2;
                        }
                    });
                };
                progressTimer.Start();

                // Create backup
                var success = await DatabaseConnection.CreateBackupAsync(backupFilePath);
                
                progressTimer.Stop();
                progressBar.Value = 100;

                if (success)
                {
                    lblStatus.Text = Resources.BackupCreated;
                    lblStatus.ForeColor = Color.FromArgb(76, 175, 80);

                    var fileInfo = new FileInfo(backupFilePath);
                    var message = $"تم إنشاء النسخة الاحتياطية بنجاح!\n\n" +
                                 $"الموقع: {backupFilePath}\n" +
                                 $"الحجم: {fileInfo.Length / 1024:N0} كيلوبايت\n" +
                                 $"التاريخ: {fileInfo.CreationTime:yyyy/MM/dd HH:mm:ss}\n\n" +
                                 "هل تريد فتح مجلد النسخة الاحتياطية؟";

                    var openFolder = MessageBox.Show(message, Resources.Success,
                        MessageBoxButtons.YesNo, MessageBoxIcon.Information,
                        MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);

                    if (openFolder == DialogResult.Yes)
                    {
                        BtnOpenBackupFolder_Click(sender, e);
                    }
                }
                else
                {
                    lblStatus.Text = "فشل في إنشاء النسخة الاحتياطية";
                    lblStatus.ForeColor = Color.Red;
                    
                    MessageBox.Show("فشل في إنشاء النسخة الاحتياطية. يرجى المحاولة مرة أخرى.", Resources.Error,
                        MessageBoxButtons.OK, MessageBoxIcon.Error,
                        MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
                }
            }
            catch (Exception ex)
            {
                lblStatus.Text = "حدث خطأ أثناء إنشاء النسخة الاحتياطية";
                lblStatus.ForeColor = Color.Red;
                progressBar.Value = 0;

                MessageBox.Show($"خطأ في إنشاء النسخة الاحتياطية: {ex.Message}", Resources.Error,
                    MessageBoxButtons.OK, MessageBoxIcon.Error,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
            }
            finally
            {
                btnCreateBackup.Enabled = true;
                btnClose.Enabled = true;
            }
        }

        private void BtnOpenBackupFolder_Click(object? sender, EventArgs e)
        {
            try
            {
                if (Directory.Exists(txtBackupPath.Text))
                {
                    System.Diagnostics.Process.Start("explorer.exe", txtBackupPath.Text);
                }
                else
                {
                    MessageBox.Show("مجلد النسخ الاحتياطي غير موجود", Resources.Error,
                        MessageBoxButtons.OK, MessageBoxIcon.Error,
                        MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"فشل في فتح المجلد: {ex.Message}", Resources.Error,
                    MessageBoxButtons.OK, MessageBoxIcon.Error,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
            }
        }

        private void BtnClose_Click(object? sender, EventArgs e)
        {
            this.Close();
        }

        #endregion

        #region Form Controls
        private TextBox txtBackupPath = null!;
        private TextBox txtBackupName = null!;
        private Button btnBrowse = null!;
        private CheckBox chkIncludeSettings = null!;
        private CheckBox chkIncludeAuditLogs = null!;
        private CheckBox chkCompressBackup = null!;
        private ProgressBar progressBar = null!;
        private Label lblStatus = null!;
        private Button btnCreateBackup = null!;
        private Button btnOpenBackupFolder = null!;
        private Button btnClose = null!;
        #endregion
    }
}
