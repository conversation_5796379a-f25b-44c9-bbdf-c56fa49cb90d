using Microsoft.Data.SqlClient;
using System.Configuration;
using System.Data;

namespace MobileShopManagement.DataAccess
{
    /// <summary>
    /// مدير الاتصال بقاعدة البيانات - Database connection manager
    /// Handles all database connections and provides connection string management
    /// </summary>
    public class DatabaseConnection
    {
        private static readonly string _connectionString;
        private static readonly object _lock = new object();

        /// <summary>
        /// Static constructor to initialize connection string
        /// </summary>
        static DatabaseConnection()
        {
            try
            {
                _connectionString = ConfigurationManager.ConnectionStrings["MobileShopDB"]?.ConnectionString
                    ?? throw new InvalidOperationException("Connection string 'MobileShopDB' not found in configuration.");
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to initialize database connection: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Gets the connection string for the database
        /// </summary>
        public static string ConnectionString => _connectionString;

        /// <summary>
        /// Creates and returns a new SQL connection
        /// </summary>
        /// <returns>New SqlConnection instance</returns>
        public static SqlConnection GetConnection()
        {
            try
            {
                var connection = new SqlConnection(_connectionString);
                return connection;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to create database connection: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Tests the database connection
        /// </summary>
        /// <returns>True if connection is successful, false otherwise</returns>
        public static async Task<bool> TestConnectionAsync()
        {
            try
            {
                using var connection = GetConnection();
                await connection.OpenAsync();
                return connection.State == ConnectionState.Open;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Executes a non-query SQL command (INSERT, UPDATE, DELETE)
        /// </summary>
        /// <param name="sql">SQL command text</param>
        /// <param name="parameters">SQL parameters</param>
        /// <returns>Number of affected rows</returns>
        public static async Task<int> ExecuteNonQueryAsync(string sql, params SqlParameter[] parameters)
        {
            try
            {
                using var connection = GetConnection();
                using var command = new SqlCommand(sql, connection);
                
                if (parameters != null)
                {
                    command.Parameters.AddRange(parameters);
                }

                await connection.OpenAsync();
                return await command.ExecuteNonQueryAsync();
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Database operation failed: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Executes a SQL query and returns a DataTable
        /// </summary>
        /// <param name="sql">SQL query text</param>
        /// <param name="parameters">SQL parameters</param>
        /// <returns>DataTable with query results</returns>
        public static async Task<DataTable> ExecuteQueryAsync(string sql, params SqlParameter[] parameters)
        {
            try
            {
                using var connection = GetConnection();
                using var command = new SqlCommand(sql, connection);
                
                if (parameters != null)
                {
                    command.Parameters.AddRange(parameters);
                }

                await connection.OpenAsync();
                using var adapter = new SqlDataAdapter(command);
                var dataTable = new DataTable();
                adapter.Fill(dataTable);
                return dataTable;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Database query failed: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Executes a SQL scalar query and returns a single value
        /// </summary>
        /// <param name="sql">SQL query text</param>
        /// <param name="parameters">SQL parameters</param>
        /// <returns>Scalar result</returns>
        public static async Task<object?> ExecuteScalarAsync(string sql, params SqlParameter[] parameters)
        {
            try
            {
                using var connection = GetConnection();
                using var command = new SqlCommand(sql, connection);
                
                if (parameters != null)
                {
                    command.Parameters.AddRange(parameters);
                }

                await connection.OpenAsync();
                return await command.ExecuteScalarAsync();
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Database scalar query failed: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Executes a stored procedure and returns a DataTable
        /// </summary>
        /// <param name="procedureName">Stored procedure name</param>
        /// <param name="parameters">SQL parameters</param>
        /// <returns>DataTable with procedure results</returns>
        public static async Task<DataTable> ExecuteStoredProcedureAsync(string procedureName, params SqlParameter[] parameters)
        {
            try
            {
                using var connection = GetConnection();
                using var command = new SqlCommand(procedureName, connection)
                {
                    CommandType = CommandType.StoredProcedure
                };
                
                if (parameters != null)
                {
                    command.Parameters.AddRange(parameters);
                }

                await connection.OpenAsync();
                using var adapter = new SqlDataAdapter(command);
                var dataTable = new DataTable();
                adapter.Fill(dataTable);
                return dataTable;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Stored procedure execution failed: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Begins a database transaction
        /// </summary>
        /// <returns>SqlTransaction instance</returns>
        public static async Task<(SqlConnection connection, SqlTransaction transaction)> BeginTransactionAsync()
        {
            try
            {
                var connection = GetConnection();
                await connection.OpenAsync();
                var transaction = connection.BeginTransaction();
                return (connection, transaction);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to begin transaction: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Creates database backup
        /// </summary>
        /// <param name="backupPath">Full path for backup file</param>
        /// <returns>True if backup successful</returns>
        public static async Task<bool> CreateBackupAsync(string backupPath)
        {
            try
            {
                var sql = $"BACKUP DATABASE MobileShopDB TO DISK = @BackupPath WITH FORMAT, INIT";
                var parameters = new[] { new SqlParameter("@BackupPath", backupPath) };
                
                await ExecuteNonQueryAsync(sql, parameters);
                return File.Exists(backupPath);
            }
            catch
            {
                return false;
            }
        }
    }
}
