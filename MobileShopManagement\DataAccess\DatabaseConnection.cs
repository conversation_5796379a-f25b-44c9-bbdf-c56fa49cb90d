using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Logging;
using MobileShopManagement.Services;
using System.Data;

namespace MobileShopManagement.DataAccess;

/// <summary>
/// مدير الاتصال بقاعدة البيانات - Database connection manager for .NET 9
/// Handles all database connections with modern async patterns and retry logic
/// </summary>
public class DatabaseConnection
{
    private static readonly object _lock = new();
    private readonly ILogger<DatabaseConnection> _logger;
    private readonly ConfigurationService _config;

    /// <summary>
    /// Constructor with dependency injection
    /// </summary>
    public DatabaseConnection(ILogger<DatabaseConnection> logger)
    {
        _logger = logger;
        _config = ConfigurationService.Instance;
    }

    /// <summary>
    /// Gets the connection string for the database
    /// </summary>
    public string ConnectionString => _config.GetConnectionString();

    /// <summary>
    /// Database settings from configuration
    /// </summary>
    private DatabaseSettings DatabaseSettings => _config.Database;

        /// <summary>
        /// Creates and returns a new SQL connection with .NET 9 optimizations
        /// </summary>
        /// <returns>New SqlConnection instance</returns>
        public SqlConnection GetConnection()
        {
            try
            {
                var connectionString = ConnectionString;
                var connection = new SqlConnection(connectionString);

                // Set command timeout from configuration
                connection.ConnectionTimeout = DatabaseSettings.ConnectionTimeout;

                _logger.LogDebug("Created new database connection");
                return connection;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create database connection");
                throw new InvalidOperationException($"Failed to create database connection: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Tests the database connection with retry logic
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if connection is successful, false otherwise</returns>
        public async Task<bool> TestConnectionAsync(CancellationToken cancellationToken = default)
        {
            var maxAttempts = DatabaseSettings.EnableRetry ? DatabaseSettings.MaxRetryAttempts : 1;

            for (int attempt = 1; attempt <= maxAttempts; attempt++)
            {
                try
                {
                    using var connection = GetConnection();
                    await connection.OpenAsync(cancellationToken);

                    var isOpen = connection.State == ConnectionState.Open;
                    _logger.LogInformation("Database connection test successful on attempt {Attempt}", attempt);
                    return isOpen;
                }
                catch (Exception ex) when (attempt < maxAttempts)
                {
                    _logger.LogWarning(ex, "Database connection test failed on attempt {Attempt}, retrying...", attempt);
                    await Task.Delay(DatabaseSettings.RetryDelay, cancellationToken);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Database connection test failed after {MaxAttempts} attempts", maxAttempts);
                    return false;
                }
            }

            return false;
        }

        /// <summary>
        /// Executes a non-query SQL command (INSERT, UPDATE, DELETE)
        /// </summary>
        /// <param name="sql">SQL command text</param>
        /// <param name="parameters">SQL parameters</param>
        /// <returns>Number of affected rows</returns>
        public static async Task<int> ExecuteNonQueryAsync(string sql, params SqlParameter[] parameters)
        {
            try
            {
                using var connection = GetConnection();
                using var command = new SqlCommand(sql, connection);
                
                if (parameters != null)
                {
                    command.Parameters.AddRange(parameters);
                }

                await connection.OpenAsync();
                return await command.ExecuteNonQueryAsync();
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Database operation failed: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Executes a SQL query and returns a DataTable
        /// </summary>
        /// <param name="sql">SQL query text</param>
        /// <param name="parameters">SQL parameters</param>
        /// <returns>DataTable with query results</returns>
        public static async Task<DataTable> ExecuteQueryAsync(string sql, params SqlParameter[] parameters)
        {
            try
            {
                using var connection = GetConnection();
                using var command = new SqlCommand(sql, connection);
                
                if (parameters != null)
                {
                    command.Parameters.AddRange(parameters);
                }

                await connection.OpenAsync();
                using var adapter = new SqlDataAdapter(command);
                var dataTable = new DataTable();
                adapter.Fill(dataTable);
                return dataTable;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Database query failed: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Executes a SQL scalar query and returns a single value
        /// </summary>
        /// <param name="sql">SQL query text</param>
        /// <param name="parameters">SQL parameters</param>
        /// <returns>Scalar result</returns>
        public static async Task<object?> ExecuteScalarAsync(string sql, params SqlParameter[] parameters)
        {
            try
            {
                using var connection = GetConnection();
                using var command = new SqlCommand(sql, connection);
                
                if (parameters != null)
                {
                    command.Parameters.AddRange(parameters);
                }

                await connection.OpenAsync();
                return await command.ExecuteScalarAsync();
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Database scalar query failed: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Executes a stored procedure and returns a DataTable
        /// </summary>
        /// <param name="procedureName">Stored procedure name</param>
        /// <param name="parameters">SQL parameters</param>
        /// <returns>DataTable with procedure results</returns>
        public static async Task<DataTable> ExecuteStoredProcedureAsync(string procedureName, params SqlParameter[] parameters)
        {
            try
            {
                using var connection = GetConnection();
                using var command = new SqlCommand(procedureName, connection)
                {
                    CommandType = CommandType.StoredProcedure
                };
                
                if (parameters != null)
                {
                    command.Parameters.AddRange(parameters);
                }

                await connection.OpenAsync();
                using var adapter = new SqlDataAdapter(command);
                var dataTable = new DataTable();
                adapter.Fill(dataTable);
                return dataTable;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Stored procedure execution failed: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Begins a database transaction
        /// </summary>
        /// <returns>SqlTransaction instance</returns>
        public static async Task<(SqlConnection connection, SqlTransaction transaction)> BeginTransactionAsync()
        {
            try
            {
                var connection = GetConnection();
                await connection.OpenAsync();
                var transaction = connection.BeginTransaction();
                return (connection, transaction);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to begin transaction: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Creates database backup
        /// </summary>
        /// <param name="backupPath">Full path for backup file</param>
        /// <returns>True if backup successful</returns>
        public static async Task<bool> CreateBackupAsync(string backupPath)
        {
            try
            {
                var sql = $"BACKUP DATABASE MobileShopDB TO DISK = @BackupPath WITH FORMAT, INIT";
                var parameters = new[] { new SqlParameter("@BackupPath", backupPath) };
                
                await ExecuteNonQueryAsync(sql, parameters);
                return File.Exists(backupPath);
            }
            catch
            {
                return false;
            }
        }
    }
}
