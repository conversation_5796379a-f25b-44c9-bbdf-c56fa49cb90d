# دليل التثبيت والإعداد
# Installation and Setup Guide

## متطلبات النظام | System Requirements

### الحد الأدنى | Minimum Requirements
- **نظام التشغيل | OS:** Windows 10 (64-bit) أو أحدث
- **المعالج | CPU:** Intel Core i3 2.0 GHz أو معادل
- **الذاكرة | RAM:** 4 GB
- **مساحة القرص | Storage:** 1 GB مساحة فارغة
- **الشاشة | Display:** 1024x768 دقة أو أعلى

### المستحسن | Recommended Requirements
- **نظام التشغيل | OS:** Windows 11 (64-bit)
- **المعالج | CPU:** Intel Core i5 3.0 GHz أو أفضل
- **الذاكرة | RAM:** 8 GB أو أكثر
- **مساحة القرص | Storage:** 2 GB مساحة فارغة
- **الشاشة | Display:** 1920x1080 دقة أو أعلى

## خطوات التثبيت | Installation Steps

### الخطوة 1: تحميل المتطلبات الأساسية
### Step 1: Download Prerequisites

#### أ. تثبيت .NET 6 Runtime
#### A. Install .NET 6 Runtime

1. اذهب إلى الرابط التالي:
   Go to: https://dotnet.microsoft.com/download/dotnet/6.0

2. حمل "ASP.NET Core Runtime 6.0.x (v6.0.x) - Windows x64"

3. شغل الملف المحمل واتبع التعليمات

#### ب. تثبيت SQL Server LocalDB
#### B. Install SQL Server LocalDB

1. حمل SQL Server Express LocalDB من:
   Download from: https://docs.microsoft.com/en-us/sql/database-engine/configure-windows/sql-server-express-localdb

2. شغل الملف المحمل:
   ```
   SqlLocalDB.msi
   ```

3. اتبع معالج التثبيت واختر الإعدادات الافتراضية

#### ج. تثبيت Visual C++ Redistributable (إذا لزم الأمر)
#### C. Install Visual C++ Redistributable (if needed)

1. حمل من: https://aka.ms/vs/17/release/vc_redist.x64.exe
2. شغل الملف واتبع التعليمات

### الخطوة 2: تحميل وإعداد التطبيق
### Step 2: Download and Setup Application

#### أ. تحميل ملفات التطبيق
#### A. Download Application Files

1. حمل أحدث إصدار من:
   Download latest release from: [GitHub Releases]

2. استخرج الملفات إلى مجلد مثل:
   Extract files to folder like:
   ```
   C:\Program Files\MobileShopManagement\
   ```

#### ب. إعداد قاعدة البيانات
#### B. Setup Database

1. افتح Command Prompt كمدير (Run as Administrator)

2. انتقل إلى مجلد التطبيق:
   Navigate to application folder:
   ```cmd
   cd "C:\Program Files\MobileShopManagement"
   ```

3. شغل سكريبت إنشاء قاعدة البيانات:
   Run database creation script:
   ```cmd
   sqlcmd -S "(LocalDB)\MSSQLLocalDB" -i "Database\CreateDatabase.sql"
   ```

4. أدرج البيانات النموذجية:
   Insert sample data:
   ```cmd
   sqlcmd -S "(LocalDB)\MSSQLLocalDB" -i "Database\SampleData.sql"
   ```

### الخطوة 3: تكوين التطبيق
### Step 3: Configure Application

#### أ. تعديل ملف الإعدادات
#### A. Edit Configuration File

1. افتح ملف `App.config` في محرر نصوص

2. تأكد من صحة سلسلة الاتصال:
   Verify connection string:
   ```xml
   <connectionStrings>
     <add name="MobileShopDB" 
          connectionString="Data Source=(LocalDB)\MSSQLLocalDB;AttachDbFilename=|DataDirectory|\MobileShopDB.mdf;Integrated Security=True;Connect Timeout=30;Encrypt=False;TrustServerCertificate=False;ApplicationIntent=ReadWrite;MultiSubnetFailover=False" 
          providerName="Microsoft.Data.SqlClient" />
   </connectionStrings>
   ```

#### ب. إنشاء المجلدات المطلوبة
#### B. Create Required Folders

قم بإنشاء المجلدات التالية:
Create the following folders:

```
C:\MobileShopBackups\
C:\MobileShopReports\
C:\MobileShopExports\
```

أو شغل الأمر التالي في Command Prompt:
Or run this command in Command Prompt:

```cmd
mkdir "C:\MobileShopBackups"
mkdir "C:\MobileShopReports"
mkdir "C:\MobileShopExports"
```

### الخطوة 4: اختبار التثبيت
### Step 4: Test Installation

#### أ. تشغيل التطبيق لأول مرة
#### A. First Run

1. انتقل إلى مجلد التطبيق
2. شغل `MobileShopManagement.exe`
3. يجب أن تظهر شاشة تسجيل الدخول

#### ب. تسجيل الدخول الأولي
#### B. Initial Login

استخدم بيانات المدير الافتراضية:
Use default admin credentials:

- **اسم المستخدم | Username:** `admin`
- **كلمة المرور | Password:** `admin123`

#### ج. التحقق من الوظائف
#### C. Verify Functions

1. تأكد من ظهور الواجهة الرئيسية
2. جرب إضافة بيع جديد
3. تحقق من عمل التقارير
4. اختبر إنشاء نسخة احتياطية

## إعداد متقدم | Advanced Configuration

### تخصيص قاعدة البيانات | Database Customization

#### استخدام SQL Server Express بدلاً من LocalDB
#### Using SQL Server Express instead of LocalDB

1. قم بتثبيت SQL Server Express
2. أنشئ قاعدة بيانات جديدة باسم `MobileShopDB`
3. عدل سلسلة الاتصال في `App.config`:

```xml
<add name="MobileShopDB" 
     connectionString="Server=.\SQLEXPRESS;Database=MobileShopDB;Integrated Security=True;Connect Timeout=30;Encrypt=False;TrustServerCertificate=False;ApplicationIntent=ReadWrite;MultiSubnetFailover=False" 
     providerName="Microsoft.Data.SqlClient" />
```

### تخصيص الإعدادات | Settings Customization

#### تغيير مجلدات النظام
#### Change System Folders

عدل القيم في `App.config`:
Edit values in `App.config`:

```xml
<appSettings>
  <add key="BackupDirectory" value="D:\MyBackups" />
  <add key="ReportsDirectory" value="D:\MyReports" />
  <add key="ExportsDirectory" value="D:\MyExports" />
</appSettings>
```

#### تخصيص إعدادات الأمان
#### Security Settings Customization

```xml
<appSettings>
  <add key="SessionTimeoutMinutes" value="60" />
  <add key="PasswordMinLength" value="8" />
  <add key="MaxLoginAttempts" value="5" />
  <add key="LockoutDurationMinutes" value="30" />
</appSettings>
```

## استكشاف أخطاء التثبيت | Installation Troubleshooting

### مشكلة: فشل في الاتصال بقاعدة البيانات
### Issue: Database Connection Failed

**الأعراض | Symptoms:**
- رسالة خطأ عند تشغيل التطبيق
- "فشل في الاتصال بقاعدة البيانات"

**الحلول | Solutions:**

1. **تحقق من تشغيل SQL Server LocalDB:**
   ```cmd
   sqllocaldb info
   sqllocaldb start MSSQLLocalDB
   ```

2. **تحقق من وجود قاعدة البيانات:**
   ```cmd
   sqlcmd -S "(LocalDB)\MSSQLLocalDB" -Q "SELECT name FROM sys.databases"
   ```

3. **أعد إنشاء قاعدة البيانات:**
   ```cmd
   sqlcmd -S "(LocalDB)\MSSQLLocalDB" -Q "DROP DATABASE IF EXISTS MobileShopDB"
   sqlcmd -S "(LocalDB)\MSSQLLocalDB" -i "Database\CreateDatabase.sql"
   ```

### مشكلة: النصوص العربية لا تظهر بشكل صحيح
### Issue: Arabic Text Not Displaying Correctly

**الحلول | Solutions:**

1. **تأكد من تثبيت خط Tahoma:**
   - اذهب إلى Control Panel > Fonts
   - تأكد من وجود خط Tahoma

2. **تحقق من إعدادات النظام:**
   - Control Panel > Region > Administrative
   - تأكد من دعم اللغة العربية

### مشكلة: التطبيق لا يبدأ
### Issue: Application Won't Start

**الحلول | Solutions:**

1. **تحقق من تثبيت .NET 6:**
   ```cmd
   dotnet --version
   ```

2. **شغل كمدير:**
   - انقر بالزر الأيمن على التطبيق
   - اختر "Run as administrator"

3. **تحقق من سجل الأحداث:**
   - Event Viewer > Windows Logs > Application
   - ابحث عن أخطاء متعلقة بالتطبيق

## التحديث | Updates

### تحديث التطبيق | Application Updates

1. **احفظ نسخة احتياطية من قاعدة البيانات**
2. **حمل الإصدار الجديد**
3. **استبدل ملفات التطبيق**
4. **شغل سكريبت التحديث إذا وجد:**
   ```cmd
   sqlcmd -S "(LocalDB)\MSSQLLocalDB" -i "Database\UpdateScript.sql"
   ```

### تحديث قاعدة البيانات | Database Updates

```sql
-- مثال على سكريبت تحديث
-- Example update script
USE MobileShopDB;
GO

-- إضافة عمود جديد
ALTER TABLE Sales ADD NewColumn NVARCHAR(100) NULL;
GO

-- تحديث البيانات الموجودة
UPDATE SystemSettings 
SET SettingValue = '1.1.0' 
WHERE SettingKey = 'Version';
GO
```

## إلغاء التثبيت | Uninstallation

### إزالة التطبيق | Remove Application

1. **احذف مجلد التطبيق:**
   ```cmd
   rmdir /s "C:\Program Files\MobileShopManagement"
   ```

2. **احذف قاعدة البيانات:**
   ```cmd
   sqlcmd -S "(LocalDB)\MSSQLLocalDB" -Q "DROP DATABASE MobileShopDB"
   ```

3. **احذف المجلدات الإضافية:**
   ```cmd
   rmdir /s "C:\MobileShopBackups"
   rmdir /s "C:\MobileShopReports"
   rmdir /s "C:\MobileShopExports"
   ```

4. **إزالة .NET 6 (اختياري):**
   - Control Panel > Programs and Features
   - ابحث عن "Microsoft .NET 6.0"
   - انقر Uninstall

## الدعم الفني | Technical Support

### معلومات الاتصال | Contact Information
- **البريد الإلكتروني | Email:** <EMAIL>
- **الهاتف | Phone:** +966-11-1234567
- **ساعات العمل | Hours:** الأحد-الخميس 9:00-17:00

### معلومات مفيدة للدعم | Useful Support Information

عند طلب الدعم، يرجى تقديم:
When requesting support, please provide:

1. **إصدار التطبيق | Application Version**
2. **نظام التشغيل | Operating System**
3. **رسالة الخطأ الكاملة | Complete Error Message**
4. **خطوات إعادة إنتاج المشكلة | Steps to Reproduce**

---

**نتمنى لك تجربة ممتعة مع نظام إدارة متجر الهواتف المحمولة!**
**We hope you enjoy using the Mobile Shop Management System!**
