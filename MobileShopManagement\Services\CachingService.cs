using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Text.Json;

namespace MobileShopManagement.Services;

/// <summary>
/// خدمة التخزين المؤقت المحدثة لـ .NET 9 - Modern caching service for .NET 9
/// Provides high-performance caching with Arabic support and automatic cleanup
/// </summary>
public sealed class CachingService : IDisposable
{
    private readonly IMemoryCache _memoryCache;
    private readonly ILogger<CachingService> _logger;
    private readonly ConfigurationService _config;
    private readonly ConcurrentDictionary<string, DateTime> _cacheKeys;
    private readonly Timer _cleanupTimer;
    private bool _disposed;

    public CachingService(IMemoryCache memoryCache, ILogger<CachingService> logger, ConfigurationService config)
    {
        _memoryCache = memoryCache;
        _logger = logger;
        _config = config;
        _cacheKeys = new ConcurrentDictionary<string, DateTime>();
        
        // Setup cleanup timer to run every 5 minutes
        _cleanupTimer = new Timer(CleanupExpiredEntries, null, TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(5));
    }

    /// <summary>
    /// Gets cache settings from configuration
    /// </summary>
    private PerformanceSettings CacheSettings => _config.Performance;

    /// <summary>
    /// Gets a cached value or creates it using the factory function
    /// </summary>
    public async Task<T> GetOrCreateAsync<T>(string key, Func<Task<T>> factory, TimeSpan? expiry = null)
    {
        if (!CacheSettings.EnableCaching)
        {
            return await factory();
        }

        var cacheKey = GenerateCacheKey(key);
        
        if (_memoryCache.TryGetValue(cacheKey, out T? cachedValue) && cachedValue != null)
        {
            _logger.LogDebug("Cache hit for key: {CacheKey}", cacheKey);
            return cachedValue;
        }

        _logger.LogDebug("Cache miss for key: {CacheKey}, creating new value", cacheKey);
        
        var value = await factory();
        await SetAsync(key, value, expiry);
        
        return value;
    }

    /// <summary>
    /// Gets a cached value or creates it using the factory function (synchronous)
    /// </summary>
    public T GetOrCreate<T>(string key, Func<T> factory, TimeSpan? expiry = null)
    {
        if (!CacheSettings.EnableCaching)
        {
            return factory();
        }

        var cacheKey = GenerateCacheKey(key);
        
        if (_memoryCache.TryGetValue(cacheKey, out T? cachedValue) && cachedValue != null)
        {
            _logger.LogDebug("Cache hit for key: {CacheKey}", cacheKey);
            return cachedValue;
        }

        _logger.LogDebug("Cache miss for key: {CacheKey}, creating new value", cacheKey);
        
        var value = factory();
        Set(key, value, expiry);
        
        return value;
    }

    /// <summary>
    /// Sets a value in the cache asynchronously
    /// </summary>
    public Task SetAsync<T>(string key, T value, TimeSpan? expiry = null)
    {
        return Task.Run(() => Set(key, value, expiry));
    }

    /// <summary>
    /// Sets a value in the cache
    /// </summary>
    public void Set<T>(string key, T value, TimeSpan? expiry = null)
    {
        if (!CacheSettings.EnableCaching)
        {
            return;
        }

        var cacheKey = GenerateCacheKey(key);
        var expiryTime = expiry ?? TimeSpan.FromMinutes(CacheSettings.CacheExpiryMinutes);
        
        var options = new MemoryCacheEntryOptions
        {
            AbsoluteExpirationRelativeToNow = expiryTime,
            SlidingExpiration = TimeSpan.FromMinutes(CacheSettings.CacheExpiryMinutes / 2),
            Priority = CacheItemPriority.Normal
        };

        // Add callback for when item is removed
        options.RegisterPostEvictionCallback((key, value, reason, state) =>
        {
            _cacheKeys.TryRemove(cacheKey, out _);
            _logger.LogDebug("Cache entry removed: {CacheKey}, Reason: {Reason}", cacheKey, reason);
        });

        _memoryCache.Set(cacheKey, value, options);
        _cacheKeys[cacheKey] = DateTime.UtcNow.Add(expiryTime);
        
        _logger.LogDebug("Cache entry set: {CacheKey}, Expiry: {Expiry}", cacheKey, expiryTime);
    }

    /// <summary>
    /// Gets a value from the cache
    /// </summary>
    public T? Get<T>(string key)
    {
        if (!CacheSettings.EnableCaching)
        {
            return default;
        }

        var cacheKey = GenerateCacheKey(key);
        
        if (_memoryCache.TryGetValue(cacheKey, out T? value))
        {
            _logger.LogDebug("Cache hit for key: {CacheKey}", cacheKey);
            return value;
        }

        _logger.LogDebug("Cache miss for key: {CacheKey}", cacheKey);
        return default;
    }

    /// <summary>
    /// Gets a value from the cache asynchronously
    /// </summary>
    public Task<T?> GetAsync<T>(string key)
    {
        return Task.FromResult(Get<T>(key));
    }

    /// <summary>
    /// Removes a value from the cache
    /// </summary>
    public void Remove(string key)
    {
        var cacheKey = GenerateCacheKey(key);
        _memoryCache.Remove(cacheKey);
        _cacheKeys.TryRemove(cacheKey, out _);
        
        _logger.LogDebug("Cache entry removed: {CacheKey}", cacheKey);
    }

    /// <summary>
    /// Removes a value from the cache asynchronously
    /// </summary>
    public Task RemoveAsync(string key)
    {
        return Task.Run(() => Remove(key));
    }

    /// <summary>
    /// Checks if a key exists in the cache
    /// </summary>
    public bool Exists(string key)
    {
        if (!CacheSettings.EnableCaching)
        {
            return false;
        }

        var cacheKey = GenerateCacheKey(key);
        return _memoryCache.TryGetValue(cacheKey, out _);
    }

    /// <summary>
    /// Clears all cache entries
    /// </summary>
    public void Clear()
    {
        if (_memoryCache is MemoryCache mc)
        {
            mc.Compact(1.0); // Remove all entries
        }
        
        _cacheKeys.Clear();
        _logger.LogInformation("Cache cleared");
    }

    /// <summary>
    /// Gets cache statistics
    /// </summary>
    public CacheStatistics GetStatistics()
    {
        var totalEntries = _cacheKeys.Count;
        var expiredEntries = _cacheKeys.Count(kvp => kvp.Value < DateTime.UtcNow);
        
        return new CacheStatistics
        {
            TotalEntries = totalEntries,
            ExpiredEntries = expiredEntries,
            ActiveEntries = totalEntries - expiredEntries,
            MaxCacheSize = CacheSettings.MaxCacheSize,
            CacheEnabled = CacheSettings.EnableCaching
        };
    }

    /// <summary>
    /// Generates a cache key with prefix
    /// </summary>
    private static string GenerateCacheKey(string key)
    {
        return $"MobileShop:{key}";
    }

    /// <summary>
    /// Cleans up expired cache entries
    /// </summary>
    private void CleanupExpiredEntries(object? state)
    {
        try
        {
            var expiredKeys = _cacheKeys
                .Where(kvp => kvp.Value < DateTime.UtcNow)
                .Select(kvp => kvp.Key)
                .ToList();

            foreach (var key in expiredKeys)
            {
                _memoryCache.Remove(key);
                _cacheKeys.TryRemove(key, out _);
            }

            if (expiredKeys.Count > 0)
            {
                _logger.LogDebug("Cleaned up {Count} expired cache entries", expiredKeys.Count);
            }

            // Check if cache size exceeds maximum
            if (_cacheKeys.Count > CacheSettings.MaxCacheSize)
            {
                var entriesToRemove = _cacheKeys.Count - CacheSettings.MaxCacheSize;
                var oldestEntries = _cacheKeys
                    .OrderBy(kvp => kvp.Value)
                    .Take(entriesToRemove)
                    .Select(kvp => kvp.Key)
                    .ToList();

                foreach (var key in oldestEntries)
                {
                    _memoryCache.Remove(key);
                    _cacheKeys.TryRemove(key, out _);
                }

                _logger.LogDebug("Removed {Count} oldest cache entries to maintain size limit", entriesToRemove);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during cache cleanup");
        }
    }

    /// <summary>
    /// Disposes the caching service
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            _cleanupTimer?.Dispose();
            _cacheKeys.Clear();
            _disposed = true;
            _logger.LogDebug("CachingService disposed");
        }
    }
}

/// <summary>
/// Cache statistics information
/// </summary>
public record CacheStatistics
{
    public int TotalEntries { get; init; }
    public int ExpiredEntries { get; init; }
    public int ActiveEntries { get; init; }
    public int MaxCacheSize { get; init; }
    public bool CacheEnabled { get; init; }
    public double HitRatio => TotalEntries > 0 ? (double)ActiveEntries / TotalEntries : 0;
}

/// <summary>
/// Extension methods for caching service
/// </summary>
public static class CachingExtensions
{
    /// <summary>
    /// Creates a cache key for user-specific data
    /// </summary>
    public static string CreateUserCacheKey(this CachingService cache, string username, string operation)
    {
        return $"User:{username}:{operation}";
    }

    /// <summary>
    /// Creates a cache key for sales data
    /// </summary>
    public static string CreateSalesCacheKey(this CachingService cache, DateTime date, string? filter = null)
    {
        var key = $"Sales:{date:yyyy-MM-dd}";
        if (!string.IsNullOrEmpty(filter))
        {
            key += $":{filter}";
        }
        return key;
    }

    /// <summary>
    /// Creates a cache key for reports
    /// </summary>
    public static string CreateReportCacheKey(this CachingService cache, string reportType, DateTime date, string? parameters = null)
    {
        var key = $"Report:{reportType}:{date:yyyy-MM-dd}";
        if (!string.IsNullOrEmpty(parameters))
        {
            key += $":{parameters}";
        }
        return key;
    }
}
