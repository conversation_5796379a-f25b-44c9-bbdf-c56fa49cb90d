using System.ComponentModel.DataAnnotations;

namespace MobileShopManagement.Models
{
    /// <summary>
    /// نموذج المستخدم - User model
    /// Represents a user in the mobile shop management system
    /// </summary>
    public class User
    {
        /// <summary>
        /// معرف المستخدم الفريد - Unique user identifier
        /// </summary>
        public int UserID { get; set; }

        /// <summary>
        /// اسم المستخدم - Username for login
        /// </summary>
        [Required(ErrorMessage = "اسم المستخدم مطلوب")]
        [StringLength(50, ErrorMessage = "اسم المستخدم يجب أن يكون أقل من 50 حرف")]
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// كلمة المرور المشفرة - Hashed password
        /// </summary>
        [Required(ErrorMessage = "كلمة المرور مطلوبة")]
        public string PasswordHash { get; set; } = string.Empty;

        /// <summary>
        /// الملح المستخدم في التشفير - Salt used for password hashing
        /// </summary>
        public string Salt { get; set; } = string.Empty;

        /// <summary>
        /// دور المستخدم - User role (Admin, Cashier)
        /// </summary>
        [Required(ErrorMessage = "دور المستخدم مطلوب")]
        public string Role { get; set; } = "Cashier";

        /// <summary>
        /// حالة المستخدم - Whether user is active
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// تاريخ الإنشاء - Creation date
        /// </summary>
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// تاريخ آخر تسجيل دخول - Last login date
        /// </summary>
        public DateTime? LastLoginDate { get; set; }

        /// <summary>
        /// عدد محاولات تسجيل الدخول الفاشلة - Failed login attempts
        /// </summary>
        public int FailedLoginAttempts { get; set; } = 0;

        /// <summary>
        /// وقت انتهاء القفل - Lockout end time
        /// </summary>
        public DateTime? LockoutEndTime { get; set; }

        /// <summary>
        /// منشئ المستخدم - User creator
        /// </summary>
        public string? CreatedBy { get; set; }

        /// <summary>
        /// تاريخ التعديل - Modification date
        /// </summary>
        public DateTime? ModifiedDate { get; set; }

        /// <summary>
        /// معدل المستخدم - User modifier
        /// </summary>
        public string? ModifiedBy { get; set; }

        /// <summary>
        /// كلمة المرور (للعرض فقط، لا تحفظ في قاعدة البيانات) - Password for display only
        /// </summary>
        [Required(ErrorMessage = "كلمة المرور مطلوبة")]
        [StringLength(100, MinimumLength = 6, ErrorMessage = "كلمة المرور يجب أن تكون بين 6 و 100 حرف")]
        public string Password { get; set; } = string.Empty;

        /// <summary>
        /// تأكيد كلمة المرور - Password confirmation
        /// </summary>
        [Compare("Password", ErrorMessage = "كلمة المرور وتأكيدها غير متطابقتين")]
        public string ConfirmPassword { get; set; } = string.Empty;

        /// <summary>
        /// Checks if user is administrator
        /// </summary>
        public bool IsAdmin => Role?.Equals("Admin", StringComparison.OrdinalIgnoreCase) == true;

        /// <summary>
        /// Checks if user is cashier
        /// </summary>
        public bool IsCashier => Role?.Equals("Cashier", StringComparison.OrdinalIgnoreCase) == true;

        /// <summary>
        /// Checks if user account is locked
        /// </summary>
        public bool IsLockedOut => LockoutEndTime.HasValue && LockoutEndTime.Value > DateTime.Now;

        /// <summary>
        /// Gets display name for the user
        /// </summary>
        public string DisplayName => $"{Username} ({Role})";

        /// <summary>
        /// Gets user status in Arabic
        /// </summary>
        public string StatusInArabic
        {
            get
            {
                if (!IsActive) return "غير نشط";
                if (IsLockedOut) return "مقفل";
                return "نشط";
            }
        }

        /// <summary>
        /// Gets role name in Arabic
        /// </summary>
        public string RoleInArabic
        {
            get
            {
                return Role switch
                {
                    "Admin" => "مدير",
                    "Cashier" => "كاشير",
                    _ => "غير محدد"
                };
            }
        }

        /// <summary>
        /// Default constructor
        /// </summary>
        public User()
        {
        }

        /// <summary>
        /// Constructor with username and role
        /// </summary>
        /// <param name="username">اسم المستخدم</param>
        /// <param name="role">دور المستخدم</param>
        public User(string username, string role)
        {
            Username = username;
            Role = role;
            CreatedDate = DateTime.Now;
        }

        /// <summary>
        /// Validates user data
        /// </summary>
        /// <returns>List of validation errors</returns>
        public List<string> Validate()
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(Username))
                errors.Add("اسم المستخدم مطلوب");

            if (Username.Length > 50)
                errors.Add("اسم المستخدم يجب أن يكون أقل من 50 حرف");

            if (string.IsNullOrWhiteSpace(Password) && UserID == 0) // New user
                errors.Add("كلمة المرور مطلوبة");

            if (!string.IsNullOrWhiteSpace(Password) && Password.Length < 6)
                errors.Add("كلمة المرور يجب أن تكون على الأقل 6 أحرف");

            if (!string.IsNullOrWhiteSpace(Password) && Password != ConfirmPassword)
                errors.Add("كلمة المرور وتأكيدها غير متطابقتين");

            if (string.IsNullOrWhiteSpace(Role))
                errors.Add("دور المستخدم مطلوب");

            if (!new[] { "Admin", "Cashier" }.Contains(Role))
                errors.Add("دور المستخدم غير صحيح");

            return errors;
        }

        /// <summary>
        /// Creates a copy of the user without sensitive data
        /// </summary>
        /// <returns>User copy without password information</returns>
        public User ToSafeUser()
        {
            return new User
            {
                UserID = UserID,
                Username = Username,
                Role = Role,
                IsActive = IsActive,
                CreatedDate = CreatedDate,
                LastLoginDate = LastLoginDate,
                FailedLoginAttempts = FailedLoginAttempts,
                LockoutEndTime = LockoutEndTime,
                CreatedBy = CreatedBy,
                ModifiedDate = ModifiedDate,
                ModifiedBy = ModifiedBy
            };
        }

        /// <summary>
        /// Returns string representation of the user
        /// </summary>
        /// <returns>User string representation</returns>
        public override string ToString()
        {
            return $"{Username} ({RoleInArabic}) - {StatusInArabic}";
        }
    }
}
