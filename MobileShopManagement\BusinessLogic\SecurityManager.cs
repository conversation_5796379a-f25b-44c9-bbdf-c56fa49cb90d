using BCrypt.Net;
using MobileShopManagement.Services;
using System.Security.Cryptography;
using System.Text;

namespace MobileShopManagement.BusinessLogic;

/// <summary>
/// مدير الأمان - Security manager for .NET 9
/// Handles password hashing, authentication, and security operations with modern patterns
/// </summary>
public static class SecurityManager
{
    private static readonly int _workFactor = 12; // BCrypt work factor for security
    private static readonly int _saltLength = 32; // Salt length in bytes

    /// <summary>
    /// Gets security settings from configuration
    /// </summary>
    private static SecuritySettings SecuritySettings => ConfigurationService.Instance.Security;

        /// <summary>
        /// Hashes a password using BCrypt with salt
        /// </summary>
        /// <param name="password">Plain text password</param>
        /// <returns>Tuple containing hashed password and salt</returns>
        public static (string hashedPassword, string salt) HashPassword(string password)
        {
            if (string.IsNullOrWhiteSpace(password))
                throw new ArgumentException("كلمة المرور لا يمكن أن تكون فارغة", nameof(password));

            try
            {
                // Generate salt
                var salt = BCrypt.Net.BCrypt.GenerateSalt(_workFactor);
                
                // Hash password with salt
                var hashedPassword = BCrypt.Net.BCrypt.HashPassword(password, salt);
                
                return (hashedPassword, salt);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"فشل في تشفير كلمة المرور: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Verifies a password against its hash
        /// </summary>
        /// <param name="password">Plain text password</param>
        /// <param name="hashedPassword">Hashed password from database</param>
        /// <returns>True if password matches, false otherwise</returns>
        public static bool VerifyPassword(string password, string hashedPassword)
        {
            if (string.IsNullOrWhiteSpace(password) || string.IsNullOrWhiteSpace(hashedPassword))
                return false;

            try
            {
                return BCrypt.Net.BCrypt.Verify(password, hashedPassword);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Generates a secure random password
        /// </summary>
        /// <param name="length">Password length (minimum 8)</param>
        /// <param name="includeSpecialChars">Include special characters</param>
        /// <returns>Generated password</returns>
        public static string GeneratePassword(int length = 12, bool includeSpecialChars = true)
        {
            if (length < 8)
                throw new ArgumentException("طول كلمة المرور يجب أن يكون على الأقل 8 أحرف", nameof(length));

            const string lowercase = "abcdefghijklmnopqrstuvwxyz";
            const string uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
            const string digits = "0123456789";
            const string specialChars = "!@#$%^&*()_+-=[]{}|;:,.<>?";

            var chars = lowercase + uppercase + digits;
            if (includeSpecialChars)
                chars += specialChars;

            var random = new Random();
            var password = new StringBuilder();

            // Ensure at least one character from each category
            password.Append(lowercase[random.Next(lowercase.Length)]);
            password.Append(uppercase[random.Next(uppercase.Length)]);
            password.Append(digits[random.Next(digits.Length)]);
            
            if (includeSpecialChars)
                password.Append(specialChars[random.Next(specialChars.Length)]);

            // Fill the rest randomly
            var remainingLength = length - password.Length;
            for (int i = 0; i < remainingLength; i++)
            {
                password.Append(chars[random.Next(chars.Length)]);
            }

            // Shuffle the password
            return new string(password.ToString().OrderBy(x => random.Next()).ToArray());
        }

        /// <summary>
        /// Validates password strength using .NET 9 pattern matching and configuration
        /// </summary>
        /// <param name="password">Password to validate</param>
        /// <returns>Validation result with score and messages</returns>
        public static PasswordValidationResult ValidatePasswordStrength(string password)
        {
            var result = new PasswordValidationResult();
            var settings = SecuritySettings;

            if (string.IsNullOrWhiteSpace(password))
            {
                result.IsValid = false;
                result.Messages.Add("كلمة المرور مطلوبة");
                return result;
            }

            var score = 0;
            var messages = new List<string>();

            // Length check using configuration
            var minLength = settings.PasswordMinLength;
            if (password.Length >= minLength)
            {
                score += 1;
            }
            else
            {
                messages.Add($"كلمة المرور يجب أن تكون على الأقل {minLength} أحرف");
            }

            if (password.Length >= 12)
                score += 1;

            // Character variety checks (only if complexity is required)
            if (settings.RequirePasswordComplexity)
            {
                if (password.Any(char.IsLower))
                    score += 1;
                else
                    messages.Add("كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل");

                if (password.Any(char.IsUpper))
                    score += 1;
                else
                    messages.Add("كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل");

                if (password.Any(char.IsDigit))
                    score += 1;
                else
                    messages.Add("كلمة المرور يجب أن تحتوي على رقم واحد على الأقل");

                if (password.Any(c => "!@#$%^&*()_+-=[]{}|;:,.<>?".Contains(c)))
                    score += 1;
            }

            // Common password check using spans for better performance
            ReadOnlySpan<string> commonPasswords = ["password", "123456", "admin", "user", "test"];
            var lowerPassword = password.ToLowerInvariant();

            foreach (var commonPassword in commonPasswords)
            {
                if (lowerPassword.Contains(commonPassword))
                {
                    score -= 2;
                    messages.Add("كلمة المرور ضعيفة جداً");
                    break;
                }
            }

            result.Score = Math.Max(0, score);
            result.IsValid = result.Score >= (settings.RequirePasswordComplexity ? 4 : 2) && messages.Count == 0;
            result.Messages = messages;

            // Determine strength level using pattern matching
            result.StrengthLevel = result.Score switch
            {
                >= 6 => "قوية جداً",
                >= 4 => "قوية",
                >= 2 => "متوسطة",
                _ => "ضعيفة"
            };

            return result;
        }

        /// <summary>
        /// Generates a secure session token
        /// </summary>
        /// <returns>Base64 encoded session token</returns>
        public static string GenerateSessionToken()
        {
            using var rng = RandomNumberGenerator.Create();
            var tokenBytes = new byte[32];
            rng.GetBytes(tokenBytes);
            return Convert.ToBase64String(tokenBytes);
        }

        /// <summary>
        /// Encrypts sensitive data using AES
        /// </summary>
        /// <param name="plainText">Text to encrypt</param>
        /// <param name="key">Encryption key</param>
        /// <returns>Encrypted text</returns>
        public static string EncryptData(string plainText, string key)
        {
            if (string.IsNullOrEmpty(plainText))
                return string.Empty;

            try
            {
                using var aes = Aes.Create();
                aes.Key = Encoding.UTF8.GetBytes(key.PadRight(32).Substring(0, 32));
                aes.IV = new byte[16]; // Use zero IV for simplicity (not recommended for production)

                using var encryptor = aes.CreateEncryptor();
                using var ms = new MemoryStream();
                using var cs = new CryptoStream(ms, encryptor, CryptoStreamMode.Write);
                using var writer = new StreamWriter(cs);
                
                writer.Write(plainText);
                writer.Close();
                
                return Convert.ToBase64String(ms.ToArray());
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"فشل في تشفير البيانات: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Decrypts data encrypted with EncryptData
        /// </summary>
        /// <param name="cipherText">Encrypted text</param>
        /// <param name="key">Decryption key</param>
        /// <returns>Decrypted text</returns>
        public static string DecryptData(string cipherText, string key)
        {
            if (string.IsNullOrEmpty(cipherText))
                return string.Empty;

            try
            {
                using var aes = Aes.Create();
                aes.Key = Encoding.UTF8.GetBytes(key.PadRight(32).Substring(0, 32));
                aes.IV = new byte[16]; // Use zero IV for simplicity

                using var decryptor = aes.CreateDecryptor();
                using var ms = new MemoryStream(Convert.FromBase64String(cipherText));
                using var cs = new CryptoStream(ms, decryptor, CryptoStreamMode.Read);
                using var reader = new StreamReader(cs);
                
                return reader.ReadToEnd();
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"فشل في فك تشفير البيانات: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Sanitizes input to prevent SQL injection
        /// </summary>
        /// <param name="input">Input string</param>
        /// <returns>Sanitized string</returns>
        public static string SanitizeInput(string input)
        {
            if (string.IsNullOrEmpty(input))
                return string.Empty;

            // Remove potentially dangerous characters
            var dangerous = new[] { "'", "\"", ";", "--", "/*", "*/", "xp_", "sp_", "exec", "execute" };
            var sanitized = input;

            foreach (var danger in dangerous)
            {
                sanitized = sanitized.Replace(danger, "", StringComparison.OrdinalIgnoreCase);
            }

            return sanitized.Trim();
        }
    }

    /// <summary>
    /// نتيجة التحقق من قوة كلمة المرور - Password validation result
    /// </summary>
    public class PasswordValidationResult
    {
        public bool IsValid { get; set; }
        public int Score { get; set; }
        public string StrengthLevel { get; set; } = string.Empty;
        public List<string> Messages { get; set; } = new List<string>();
    }
}
