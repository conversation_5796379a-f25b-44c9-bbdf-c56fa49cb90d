-- Mobile Shop Management System Database Creation Script
-- Target: SQL Server LocalDB/Express
-- Encoding: UTF-8 with Arabic support

USE master;
GO

-- Create database if it doesn't exist
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'MobileShopDB')
BEGIN
    CREATE DATABASE MobileShopDB
    COLLATE Arabic_CI_AS;
END
GO

USE MobileShopDB;
GO

-- Enable Arabic language support
SET LANGUAGE Arabic;
GO

-- Create Users table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Users' AND xtype='U')
BEGIN
    CREATE TABLE Users (
        UserID int IDENTITY(1,1) PRIMARY KEY,
        Username nvarchar(50) NOT NULL UNIQUE,
        PasswordHash nvarchar(255) NOT NULL,
        Salt nvarchar(255) NOT NULL,
        Role nvarchar(20) NOT NULL DEFAULT 'Cashier',
        IsActive bit NOT NULL DEFAULT 1,
        CreatedDate datetime2 NOT NULL DEFAULT GETDATE(),
        LastLoginDate datetime2 NULL,
        FailedLoginAttempts int NOT NULL DEFAULT 0,
        LockoutEndTime datetime2 NULL,
        CreatedBy nvarchar(50) NULL,
        ModifiedDate datetime2 NULL,
        ModifiedBy nvarchar(50) NULL
    );
    
    -- Create indexes for performance
    CREATE INDEX IX_Users_Username ON Users(Username);
    CREATE INDEX IX_Users_Role ON Users(Role);
    CREATE INDEX IX_Users_IsActive ON Users(IsActive);
END
GO

-- Create Sales table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Sales' AND xtype='U')
BEGIN
    CREATE TABLE Sales (
        SaleID int IDENTITY(1,1) PRIMARY KEY,
        ProductName nvarchar(100) NOT NULL,
        SalePrice decimal(10,2) NOT NULL CHECK (SalePrice > 0),
        SaleDate datetime2 NOT NULL DEFAULT GETDATE(),
        SaleDay AS (DATENAME(weekday, SaleDate)) PERSISTED,
        QuantitySold int NOT NULL DEFAULT 1 CHECK (QuantitySold > 0),
        CustomerName nvarchar(100) NULL,
        CustomerPhone nvarchar(20) NULL,
        Notes nvarchar(500) NULL,
        CreatedBy nvarchar(50) NOT NULL,
        CreatedDate datetime2 NOT NULL DEFAULT GETDATE(),
        ModifiedDate datetime2 NULL,
        ModifiedBy nvarchar(50) NULL,
        IsDeleted bit NOT NULL DEFAULT 0,
        DeletedDate datetime2 NULL,
        DeletedBy nvarchar(50) NULL,
        
        -- Foreign key constraint
        CONSTRAINT FK_Sales_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(Username)
    );
    
    -- Create indexes for performance
    CREATE INDEX IX_Sales_SaleDate ON Sales(SaleDate);
    CREATE INDEX IX_Sales_ProductName ON Sales(ProductName);
    CREATE INDEX IX_Sales_CustomerName ON Sales(CustomerName);
    CREATE INDEX IX_Sales_CreatedBy ON Sales(CreatedBy);
    CREATE INDEX IX_Sales_IsDeleted ON Sales(IsDeleted);
END
GO

-- Create AuditLog table for tracking changes
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='AuditLog' AND xtype='U')
BEGIN
    CREATE TABLE AuditLog (
        AuditID int IDENTITY(1,1) PRIMARY KEY,
        TableName nvarchar(50) NOT NULL,
        RecordID int NOT NULL,
        Action nvarchar(10) NOT NULL, -- INSERT, UPDATE, DELETE
        OldValues nvarchar(max) NULL,
        NewValues nvarchar(max) NULL,
        ChangedBy nvarchar(50) NOT NULL,
        ChangedDate datetime2 NOT NULL DEFAULT GETDATE(),
        IPAddress nvarchar(45) NULL,
        UserAgent nvarchar(500) NULL
    );
    
    CREATE INDEX IX_AuditLog_TableName ON AuditLog(TableName);
    CREATE INDEX IX_AuditLog_RecordID ON AuditLog(RecordID);
    CREATE INDEX IX_AuditLog_ChangedDate ON AuditLog(ChangedDate);
END
GO

-- Create SystemSettings table for application configuration
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='SystemSettings' AND xtype='U')
BEGIN
    CREATE TABLE SystemSettings (
        SettingID int IDENTITY(1,1) PRIMARY KEY,
        SettingKey nvarchar(100) NOT NULL UNIQUE,
        SettingValue nvarchar(max) NOT NULL,
        Description nvarchar(500) NULL,
        Category nvarchar(50) NULL,
        IsUserConfigurable bit NOT NULL DEFAULT 1,
        CreatedDate datetime2 NOT NULL DEFAULT GETDATE(),
        ModifiedDate datetime2 NULL,
        ModifiedBy nvarchar(50) NULL
    );
END
GO

-- Create stored procedures for common operations
-- Stored procedure for user authentication
CREATE OR ALTER PROCEDURE sp_AuthenticateUser
    @Username NVARCHAR(50),
    @PasswordHash NVARCHAR(255)
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @UserID INT, @IsActive BIT, @FailedAttempts INT, @LockoutEnd DATETIME2;

    SELECT @UserID = UserID, @IsActive = IsActive,
           @FailedAttempts = FailedLoginAttempts, @LockoutEnd = LockoutEndTime
    FROM Users
    WHERE Username = @Username AND PasswordHash = @PasswordHash;

    IF @UserID IS NOT NULL AND @IsActive = 1 AND (@LockoutEnd IS NULL OR @LockoutEnd < GETDATE())
    BEGIN
        -- Successful login - reset failed attempts and update last login
        UPDATE Users
        SET FailedLoginAttempts = 0, LastLoginDate = GETDATE(), LockoutEndTime = NULL
        WHERE UserID = @UserID;

        SELECT UserID, Username, Role, 'SUCCESS' AS Result;
    END
    ELSE
    BEGIN
        SELECT NULL AS UserID, NULL AS Username, NULL AS Role, 'FAILED' AS Result;
    END
END
GO

-- Stored procedure for getting sales data with filters
CREATE OR ALTER PROCEDURE sp_GetSalesData
    @StartDate DATETIME2 = NULL,
    @EndDate DATETIME2 = NULL,
    @ProductName NVARCHAR(100) = NULL,
    @CustomerName NVARCHAR(100) = NULL,
    @CreatedBy NVARCHAR(50) = NULL,
    @IncludeDeleted BIT = 0
AS
BEGIN
    SET NOCOUNT ON;

    SELECT
        SaleID,
        ProductName,
        SalePrice,
        SaleDate,
        SaleDay,
        QuantitySold,
        CustomerName,
        CustomerPhone,
        Notes,
        CreatedBy,
        CreatedDate,
        (SalePrice * QuantitySold) AS TotalAmount
    FROM Sales
    WHERE
        (@StartDate IS NULL OR SaleDate >= @StartDate)
        AND (@EndDate IS NULL OR SaleDate <= @EndDate)
        AND (@ProductName IS NULL OR ProductName LIKE '%' + @ProductName + '%')
        AND (@CustomerName IS NULL OR CustomerName LIKE '%' + @CustomerName + '%')
        AND (@CreatedBy IS NULL OR CreatedBy = @CreatedBy)
        AND (IsDeleted = 0 OR @IncludeDeleted = 1)
    ORDER BY SaleDate DESC;
END
GO

-- Stored procedure for daily sales summary
CREATE OR ALTER PROCEDURE sp_GetDailySalesSummary
    @SaleDate DATE
AS
BEGIN
    SET NOCOUNT ON;

    SELECT
        COUNT(*) AS TotalTransactions,
        SUM(SalePrice * QuantitySold) AS TotalRevenue,
        AVG(SalePrice * QuantitySold) AS AverageTransaction,
        MIN(SalePrice * QuantitySold) AS MinTransaction,
        MAX(SalePrice * QuantitySold) AS MaxTransaction,
        COUNT(DISTINCT CustomerName) AS UniqueCustomers
    FROM Sales
    WHERE CAST(SaleDate AS DATE) = @SaleDate AND IsDeleted = 0;

    -- Top selling products for the day
    SELECT TOP 5
        ProductName,
        SUM(QuantitySold) AS TotalQuantity,
        SUM(SalePrice * QuantitySold) AS TotalRevenue,
        COUNT(*) AS TransactionCount
    FROM Sales
    WHERE CAST(SaleDate AS DATE) = @SaleDate AND IsDeleted = 0
    GROUP BY ProductName
    ORDER BY TotalRevenue DESC;
END
GO

PRINT 'Database tables and stored procedures created successfully - تم إنشاء جداول قاعدة البيانات والإجراءات المخزنة بنجاح';
GO
