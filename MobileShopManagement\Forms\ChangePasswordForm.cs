using MobileShopManagement.BusinessLogic;
using MobileShopManagement.Properties;
using System.Configuration;

namespace MobileShopManagement.Forms
{
    /// <summary>
    /// نموذج تغيير كلمة المرور - Change password form
    /// Form for changing user password
    /// </summary>
    public partial class ChangePasswordForm : Form
    {
        private readonly UserManager _userManager;

        /// <summary>
        /// Constructor
        /// </summary>
        public ChangePasswordForm()
        {
            InitializeComponent();
            _userManager = new UserManager();
            SetupForm();
        }

        /// <summary>
        /// Sets up the form with Arabic RTL support and styling
        /// </summary>
        private void SetupForm()
        {
            // Set RTL support
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // Set form properties
            this.Text = "تغيير كلمة المرور";
            this.Size = new Size(450, 350);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.BackColor = Color.FromArgb(250, 250, 250);

            // Set font
            var fontName = ConfigurationManager.AppSettings["DefaultFont"] ?? "Tahoma";
            var fontSize = float.TryParse(ConfigurationManager.AppSettings["DefaultFontSize"], out var size) ? size : 9f;
            this.Font = new Font(fontName, fontSize);

            CreateControls();
            SetupEventHandlers();
        }

        /// <summary>
        /// Creates and positions form controls
        /// </summary>
        private void CreateControls()
        {
            // Title panel
            var titlePanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 60,
                BackColor = Color.FromArgb(63, 81, 181),
                Padding = new Padding(20)
            };

            var lblTitle = new Label
            {
                Text = "تغيير كلمة المرور",
                Font = new Font(this.Font.FontFamily, 14, FontStyle.Bold),
                ForeColor = Color.White,
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter
            };
            titlePanel.Controls.Add(lblTitle);
            this.Controls.Add(titlePanel);

            // Main content panel
            var contentPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(30, 20, 30, 20)
            };

            var yPos = 20;
            var labelWidth = 150;
            var controlWidth = 250;
            var spacing = 40;

            // Current user info
            var lblCurrentUser = new Label
            {
                Text = $"المستخدم الحالي: {SessionManager.CurrentUsername}",
                Font = new Font(this.Font.FontFamily, 10, FontStyle.Bold),
                ForeColor = Color.FromArgb(33, 150, 243),
                Location = new Point(30, yPos),
                Size = new Size(350, 25),
                TextAlign = ContentAlignment.MiddleCenter
            };
            contentPanel.Controls.Add(lblCurrentUser);
            yPos += 40;

            // Current Password
            var lblCurrentPassword = new Label
            {
                Text = "كلمة المرور الحالية *:",
                Location = new Point(250, yPos),
                Size = new Size(labelWidth, 25),
                TextAlign = ContentAlignment.MiddleRight
            };
            contentPanel.Controls.Add(lblCurrentPassword);

            txtCurrentPassword = new TextBox
            {
                Location = new Point(30, yPos),
                Size = new Size(controlWidth, 25),
                UseSystemPasswordChar = true,
                Font = new Font(this.Font.FontFamily, 10),
                TextAlign = HorizontalAlignment.Right
            };
            contentPanel.Controls.Add(txtCurrentPassword);
            yPos += spacing;

            // New Password
            var lblNewPassword = new Label
            {
                Text = "كلمة المرور الجديدة *:",
                Location = new Point(250, yPos),
                Size = new Size(labelWidth, 25),
                TextAlign = ContentAlignment.MiddleRight
            };
            contentPanel.Controls.Add(lblNewPassword);

            txtNewPassword = new TextBox
            {
                Location = new Point(30, yPos),
                Size = new Size(controlWidth, 25),
                UseSystemPasswordChar = true,
                Font = new Font(this.Font.FontFamily, 10),
                TextAlign = HorizontalAlignment.Right
            };
            contentPanel.Controls.Add(txtNewPassword);
            yPos += spacing;

            // Confirm New Password
            var lblConfirmPassword = new Label
            {
                Text = "تأكيد كلمة المرور الجديدة *:",
                Location = new Point(250, yPos),
                Size = new Size(labelWidth, 25),
                TextAlign = ContentAlignment.MiddleRight
            };
            contentPanel.Controls.Add(lblConfirmPassword);

            txtConfirmPassword = new TextBox
            {
                Location = new Point(30, yPos),
                Size = new Size(controlWidth, 25),
                UseSystemPasswordChar = true,
                Font = new Font(this.Font.FontFamily, 10),
                TextAlign = HorizontalAlignment.Right
            };
            contentPanel.Controls.Add(txtConfirmPassword);
            yPos += spacing;

            // Password strength indicator
            lblPasswordStrength = new Label
            {
                Text = "",
                Location = new Point(30, yPos),
                Size = new Size(350, 20),
                TextAlign = ContentAlignment.MiddleCenter,
                Font = new Font(this.Font.FontFamily, 8)
            };
            contentPanel.Controls.Add(lblPasswordStrength);
            yPos += 30;

            // Buttons
            btnChangePassword = new Button
            {
                Text = "تغيير كلمة المرور",
                Location = new Point(200, yPos),
                Size = new Size(130, 35),
                BackColor = Color.FromArgb(76, 175, 80),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font(this.Font.FontFamily, 10, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            btnChangePassword.FlatAppearance.BorderSize = 0;
            contentPanel.Controls.Add(btnChangePassword);

            btnCancel = new Button
            {
                Text = Resources.Cancel,
                Location = new Point(50, yPos),
                Size = new Size(100, 35),
                BackColor = Color.FromArgb(158, 158, 158),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font(this.Font.FontFamily, 10, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            btnCancel.FlatAppearance.BorderSize = 0;
            contentPanel.Controls.Add(btnCancel);

            // Required fields note
            var lblRequired = new Label
            {
                Text = "* الحقول المطلوبة",
                Location = new Point(30, yPos + 45),
                Size = new Size(200, 20),
                ForeColor = Color.Red,
                Font = new Font(this.Font.FontFamily, 8)
            };
            contentPanel.Controls.Add(lblRequired);

            // Password requirements
            var lblRequirements = new Label
            {
                Text = "متطلبات كلمة المرور:\n" +
                       "• على الأقل 6 أحرف\n" +
                       "• حرف كبير وحرف صغير\n" +
                       "• رقم واحد على الأقل",
                Location = new Point(30, yPos + 70),
                Size = new Size(350, 60),
                ForeColor = Color.Gray,
                Font = new Font(this.Font.FontFamily, 8)
            };
            contentPanel.Controls.Add(lblRequirements);

            this.Controls.Add(contentPanel);
        }

        /// <summary>
        /// Sets up event handlers
        /// </summary>
        private void SetupEventHandlers()
        {
            btnChangePassword.Click += BtnChangePassword_Click;
            btnCancel.Click += BtnCancel_Click;
            txtNewPassword.TextChanged += TxtNewPassword_TextChanged;
            
            // Set tab order
            txtCurrentPassword.TabIndex = 0;
            txtNewPassword.TabIndex = 1;
            txtConfirmPassword.TabIndex = 2;
            btnChangePassword.TabIndex = 3;
            btnCancel.TabIndex = 4;

            this.AcceptButton = btnChangePassword;
            this.CancelButton = btnCancel;

            // Focus on current password
            this.Load += (s, e) => txtCurrentPassword.Focus();
        }

        /// <summary>
        /// New password text changed event handler
        /// </summary>
        private void TxtNewPassword_TextChanged(object? sender, EventArgs e)
        {
            UpdatePasswordStrength();
        }

        /// <summary>
        /// Updates password strength indicator
        /// </summary>
        private void UpdatePasswordStrength()
        {
            if (string.IsNullOrWhiteSpace(txtNewPassword.Text))
            {
                lblPasswordStrength.Text = "";
                return;
            }

            var validation = SecurityManager.ValidatePasswordStrength(txtNewPassword.Text);
            lblPasswordStrength.Text = $"قوة كلمة المرور: {validation.StrengthLevel}";
            
            lblPasswordStrength.ForeColor = validation.StrengthLevel switch
            {
                "قوية جداً" => Color.Green,
                "قوية" => Color.FromArgb(76, 175, 80),
                "متوسطة" => Color.Orange,
                _ => Color.Red
            };
        }

        /// <summary>
        /// Change password button click event handler
        /// </summary>
        private async void BtnChangePassword_Click(object? sender, EventArgs e)
        {
            try
            {
                // Validate input
                if (!ValidateInput())
                    return;

                // Show loading
                btnChangePassword.Enabled = false;
                btnChangePassword.Text = "جاري التغيير...";
                this.Cursor = Cursors.WaitCursor;

                // Change password
                var success = await _userManager.ChangePasswordAsync(
                    SessionManager.CurrentUsername,
                    txtCurrentPassword.Text,
                    txtNewPassword.Text);

                if (success)
                {
                    MessageBox.Show("تم تغيير كلمة المرور بنجاح", Resources.Success,
                        MessageBoxButtons.OK, MessageBoxIcon.Information,
                        MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);

                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    MessageBox.Show("فشل في تغيير كلمة المرور", Resources.Error,
                        MessageBoxButtons.OK, MessageBoxIcon.Error,
                        MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
                }
            }
            catch (UnauthorizedAccessException)
            {
                MessageBox.Show("كلمة المرور الحالية غير صحيحة", Resources.Error,
                    MessageBoxButtons.OK, MessageBoxIcon.Error,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
                txtCurrentPassword.Focus();
                txtCurrentPassword.SelectAll();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تغيير كلمة المرور: {ex.Message}", Resources.Error,
                    MessageBoxButtons.OK, MessageBoxIcon.Error,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
            }
            finally
            {
                btnChangePassword.Enabled = true;
                btnChangePassword.Text = "تغيير كلمة المرور";
                this.Cursor = Cursors.Default;
            }
        }

        /// <summary>
        /// Cancel button click event handler
        /// </summary>
        private void BtnCancel_Click(object? sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        /// <summary>
        /// Validates form input
        /// </summary>
        /// <returns>True if input is valid</returns>
        private bool ValidateInput()
        {
            var errors = new List<string>();

            // Current password validation
            if (string.IsNullOrWhiteSpace(txtCurrentPassword.Text))
                errors.Add("كلمة المرور الحالية مطلوبة");

            // New password validation
            if (string.IsNullOrWhiteSpace(txtNewPassword.Text))
                errors.Add("كلمة المرور الجديدة مطلوبة");
            else
            {
                var validation = SecurityManager.ValidatePasswordStrength(txtNewPassword.Text);
                if (!validation.IsValid)
                    errors.AddRange(validation.Messages);
            }

            // Confirm password validation
            if (string.IsNullOrWhiteSpace(txtConfirmPassword.Text))
                errors.Add("تأكيد كلمة المرور مطلوب");
            else if (txtNewPassword.Text != txtConfirmPassword.Text)
                errors.Add("كلمة المرور الجديدة وتأكيدها غير متطابقتين");

            // Check if new password is different from current
            if (txtCurrentPassword.Text == txtNewPassword.Text)
                errors.Add("كلمة المرور الجديدة يجب أن تكون مختلفة عن الحالية");

            if (errors.Any())
            {
                var errorMessage = string.Join("\n", errors);
                MessageBox.Show(errorMessage, "بيانات غير صحيحة",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
                return false;
            }

            return true;
        }

        #region Form Controls
        private TextBox txtCurrentPassword = null!;
        private TextBox txtNewPassword = null!;
        private TextBox txtConfirmPassword = null!;
        private Label lblPasswordStrength = null!;
        private Button btnChangePassword = null!;
        private Button btnCancel = null!;
        #endregion
    }
}
