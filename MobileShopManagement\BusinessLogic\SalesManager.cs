using MobileShopManagement.DataAccess;
using MobileShopManagement.Models;
using Microsoft.Data.SqlClient;
using System.Data;

namespace MobileShopManagement.BusinessLogic
{
    /// <summary>
    /// مدير المبيعات - Sales manager
    /// Handles all sales-related business operations
    /// </summary>
    public class SalesManager
    {
        /// <summary>
        /// Creates a new sale
        /// </summary>
        /// <param name="sale">Sale object with details</param>
        /// <returns>Created sale ID</returns>
        public async Task<int> CreateSaleAsync(Sale sale)
        {
            try
            {
                // Validate sale data
                var validationErrors = sale.Validate();
                if (validationErrors.Any())
                    throw new ArgumentException($"بيانات البيع غير صحيحة: {string.Join(", ", validationErrors)}");

                // Validate permissions
                SessionManager.ValidatePermission(UserPermission.AddSale);

                // Insert sale into database
                var sql = @"
                    INSERT INTO Sales (ProductName, SalePrice, SaleDate, QuantitySold, CustomerName, CustomerPhone, Notes, CreatedBy)
                    VALUES (@ProductName, @SalePrice, @SaleDate, @QuantitySold, @CustomerName, @CustomerPhone, @Notes, @CreatedBy);
                    SELECT SCOPE_IDENTITY();";

                var parameters = new[]
                {
                    new SqlParameter("@ProductName", sale.ProductName),
                    new SqlParameter("@SalePrice", sale.SalePrice),
                    new SqlParameter("@SaleDate", sale.SaleDate),
                    new SqlParameter("@QuantitySold", sale.QuantitySold),
                    new SqlParameter("@CustomerName", (object?)sale.CustomerName ?? DBNull.Value),
                    new SqlParameter("@CustomerPhone", (object?)sale.CustomerPhone ?? DBNull.Value),
                    new SqlParameter("@Notes", (object?)sale.Notes ?? DBNull.Value),
                    new SqlParameter("@CreatedBy", sale.CreatedBy)
                };

                var result = await DatabaseConnection.ExecuteScalarAsync(sql, parameters);
                var saleId = Convert.ToInt32(result);

                // Log audit trail
                await LogAuditAsync("Sales", saleId, "INSERT", $"Sale created: {sale.ProductName} - {sale.TotalAmountFormatted}", sale.CreatedBy);

                return saleId;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"فشل في إنشاء البيع: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Updates an existing sale
        /// </summary>
        /// <param name="sale">Sale object with updated details</param>
        /// <param name="modifiedBy">Username of modifier</param>
        /// <returns>True if update successful</returns>
        public async Task<bool> UpdateSaleAsync(Sale sale, string modifiedBy)
        {
            try
            {
                // Validate sale data
                var validationErrors = sale.Validate();
                if (validationErrors.Any())
                    throw new ArgumentException($"بيانات البيع غير صحيحة: {string.Join(", ", validationErrors)}");

                // Validate permissions
                SessionManager.ValidatePermission(UserPermission.EditSale);

                // Get original sale for audit
                var originalSale = await GetSaleByIdAsync(sale.SaleID);
                if (originalSale == null)
                    throw new InvalidOperationException("البيع غير موجود");

                // Update sale in database
                var sql = @"
                    UPDATE Sales 
                    SET ProductName = @ProductName, SalePrice = @SalePrice, SaleDate = @SaleDate, 
                        QuantitySold = @QuantitySold, CustomerName = @CustomerName, CustomerPhone = @CustomerPhone,
                        Notes = @Notes, ModifiedDate = GETDATE(), ModifiedBy = @ModifiedBy
                    WHERE SaleID = @SaleID AND IsDeleted = 0";

                var parameters = new[]
                {
                    new SqlParameter("@SaleID", sale.SaleID),
                    new SqlParameter("@ProductName", sale.ProductName),
                    new SqlParameter("@SalePrice", sale.SalePrice),
                    new SqlParameter("@SaleDate", sale.SaleDate),
                    new SqlParameter("@QuantitySold", sale.QuantitySold),
                    new SqlParameter("@CustomerName", (object?)sale.CustomerName ?? DBNull.Value),
                    new SqlParameter("@CustomerPhone", (object?)sale.CustomerPhone ?? DBNull.Value),
                    new SqlParameter("@Notes", (object?)sale.Notes ?? DBNull.Value),
                    new SqlParameter("@ModifiedBy", modifiedBy)
                };

                var rowsAffected = await DatabaseConnection.ExecuteNonQueryAsync(sql, parameters);

                if (rowsAffected > 0)
                {
                    // Log audit trail
                    var changes = $"Updated: {originalSale.ProductName} -> {sale.ProductName}, {originalSale.TotalAmountFormatted} -> {sale.TotalAmountFormatted}";
                    await LogAuditAsync("Sales", sale.SaleID, "UPDATE", changes, modifiedBy);
                }

                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"فشل في تحديث البيع: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Deletes a sale (soft delete)
        /// </summary>
        /// <param name="saleId">Sale ID to delete</param>
        /// <param name="deletedBy">Username of deleter</param>
        /// <returns>True if deletion successful</returns>
        public async Task<bool> DeleteSaleAsync(int saleId, string deletedBy)
        {
            try
            {
                // Validate permissions
                SessionManager.ValidatePermission(UserPermission.DeleteSale);

                // Get sale for audit
                var sale = await GetSaleByIdAsync(saleId);
                if (sale == null)
                    throw new InvalidOperationException("البيع غير موجود");

                var sql = @"
                    UPDATE Sales 
                    SET IsDeleted = 1, DeletedDate = GETDATE(), DeletedBy = @DeletedBy
                    WHERE SaleID = @SaleID";

                var parameters = new[]
                {
                    new SqlParameter("@SaleID", saleId),
                    new SqlParameter("@DeletedBy", deletedBy)
                };

                var rowsAffected = await DatabaseConnection.ExecuteNonQueryAsync(sql, parameters);

                if (rowsAffected > 0)
                {
                    // Log audit trail
                    await LogAuditAsync("Sales", saleId, "DELETE", $"Sale deleted: {sale.ProductName} - {sale.TotalAmountFormatted}", deletedBy);
                }

                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"فشل في حذف البيع: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Gets a sale by ID
        /// </summary>
        /// <param name="saleId">Sale ID to retrieve</param>
        /// <returns>Sale object if found, null otherwise</returns>
        public async Task<Sale?> GetSaleByIdAsync(int saleId)
        {
            try
            {
                var sql = @"
                    SELECT SaleID, ProductName, SalePrice, SaleDate, SaleDay, QuantitySold, 
                           CustomerName, CustomerPhone, Notes, CreatedBy, CreatedDate, 
                           ModifiedDate, ModifiedBy, IsDeleted, DeletedDate, DeletedBy
                    FROM Sales 
                    WHERE SaleID = @SaleID";

                var parameters = new[] { new SqlParameter("@SaleID", saleId) };
                var dataTable = await DatabaseConnection.ExecuteQueryAsync(sql, parameters);

                if (dataTable.Rows.Count == 0)
                    return null;

                var row = dataTable.Rows[0];
                return MapDataRowToSale(row);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"فشل في البحث عن البيع: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Gets sales data with filters
        /// </summary>
        /// <param name="filters">Search filters</param>
        /// <returns>List of sales</returns>
        public async Task<List<Sale>> GetSalesAsync(SalesSearchFilters filters)
        {
            try
            {
                // Validate permissions
                SessionManager.ValidatePermission(UserPermission.ViewSales);

                var parameters = new List<SqlParameter>();
                var sql = @"
                    SELECT SaleID, ProductName, SalePrice, SaleDate, SaleDay, QuantitySold, 
                           CustomerName, CustomerPhone, Notes, CreatedBy, CreatedDate, 
                           ModifiedDate, ModifiedBy, IsDeleted, DeletedDate, DeletedBy
                    FROM Sales 
                    WHERE IsDeleted = 0";

                // Apply filters
                if (filters.StartDate.HasValue)
                {
                    sql += " AND SaleDate >= @StartDate";
                    parameters.Add(new SqlParameter("@StartDate", filters.StartDate.Value));
                }

                if (filters.EndDate.HasValue)
                {
                    sql += " AND SaleDate <= @EndDate";
                    parameters.Add(new SqlParameter("@EndDate", filters.EndDate.Value.AddDays(1).AddSeconds(-1)));
                }

                if (!string.IsNullOrWhiteSpace(filters.ProductName))
                {
                    sql += " AND ProductName LIKE @ProductName";
                    parameters.Add(new SqlParameter("@ProductName", $"%{filters.ProductName}%"));
                }

                if (!string.IsNullOrWhiteSpace(filters.CustomerName))
                {
                    sql += " AND CustomerName LIKE @CustomerName";
                    parameters.Add(new SqlParameter("@CustomerName", $"%{filters.CustomerName}%"));
                }

                if (!string.IsNullOrWhiteSpace(filters.CreatedBy))
                {
                    sql += " AND CreatedBy = @CreatedBy";
                    parameters.Add(new SqlParameter("@CreatedBy", filters.CreatedBy));
                }

                if (filters.MinAmount.HasValue)
                {
                    sql += " AND (SalePrice * QuantitySold) >= @MinAmount";
                    parameters.Add(new SqlParameter("@MinAmount", filters.MinAmount.Value));
                }

                if (filters.MaxAmount.HasValue)
                {
                    sql += " AND (SalePrice * QuantitySold) <= @MaxAmount";
                    parameters.Add(new SqlParameter("@MaxAmount", filters.MaxAmount.Value));
                }

                sql += " ORDER BY SaleDate DESC";

                var dataTable = await DatabaseConnection.ExecuteQueryAsync(sql, parameters.ToArray());
                var sales = new List<Sale>();

                foreach (DataRow row in dataTable.Rows)
                {
                    sales.Add(MapDataRowToSale(row));
                }

                return sales;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"فشل في جلب المبيعات: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Gets daily sales summary
        /// </summary>
        /// <param name="date">Date to get summary for</param>
        /// <returns>Daily sales summary</returns>
        public async Task<DailySalesSummary> GetDailySalesSummaryAsync(DateTime date)
        {
            try
            {
                // Validate permissions
                SessionManager.ValidatePermission(UserPermission.ViewReports);

                var parameters = new[] { new SqlParameter("@SaleDate", date.Date) };
                var dataTable = await DatabaseConnection.ExecuteStoredProcedureAsync("sp_GetDailySalesSummary", parameters);

                var summary = new DailySalesSummary
                {
                    Date = date.Date,
                    TotalTransactions = 0,
                    TotalRevenue = 0,
                    AverageTransaction = 0,
                    MinTransaction = 0,
                    MaxTransaction = 0,
                    UniqueCustomers = 0,
                    TopProducts = new List<ProductSummary>()
                };

                if (dataTable.Rows.Count > 0)
                {
                    var row = dataTable.Rows[0];
                    summary.TotalTransactions = Convert.ToInt32(row["TotalTransactions"]);
                    summary.TotalRevenue = Convert.ToDecimal(row["TotalRevenue"]);
                    summary.AverageTransaction = Convert.ToDecimal(row["AverageTransaction"]);
                    summary.MinTransaction = Convert.ToDecimal(row["MinTransaction"]);
                    summary.MaxTransaction = Convert.ToDecimal(row["MaxTransaction"]);
                    summary.UniqueCustomers = Convert.ToInt32(row["UniqueCustomers"]);
                }

                // Get top products (second result set)
                if (dataTable.Rows.Count > 1)
                {
                    for (int i = 1; i < dataTable.Rows.Count; i++)
                    {
                        var row = dataTable.Rows[i];
                        summary.TopProducts.Add(new ProductSummary
                        {
                            ProductName = row["ProductName"].ToString() ?? string.Empty,
                            TotalQuantity = Convert.ToInt32(row["TotalQuantity"]),
                            TotalRevenue = Convert.ToDecimal(row["TotalRevenue"]),
                            TransactionCount = Convert.ToInt32(row["TransactionCount"])
                        });
                    }
                }

                return summary;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"فشل في جلب ملخص المبيعات اليومية: {ex.Message}", ex);
            }
        }

        #region Private Helper Methods

        /// <summary>
        /// Maps a DataRow to Sale object
        /// </summary>
        private static Sale MapDataRowToSale(DataRow row)
        {
            return new Sale
            {
                SaleID = Convert.ToInt32(row["SaleID"]),
                ProductName = row["ProductName"].ToString() ?? string.Empty,
                SalePrice = Convert.ToDecimal(row["SalePrice"]),
                SaleDate = Convert.ToDateTime(row["SaleDate"]),
                SaleDay = row["SaleDay"].ToString() ?? string.Empty,
                QuantitySold = Convert.ToInt32(row["QuantitySold"]),
                CustomerName = row["CustomerName"] == DBNull.Value ? null : row["CustomerName"].ToString(),
                CustomerPhone = row["CustomerPhone"] == DBNull.Value ? null : row["CustomerPhone"].ToString(),
                Notes = row["Notes"] == DBNull.Value ? null : row["Notes"].ToString(),
                CreatedBy = row["CreatedBy"].ToString() ?? string.Empty,
                CreatedDate = Convert.ToDateTime(row["CreatedDate"]),
                ModifiedDate = row["ModifiedDate"] == DBNull.Value ? null : Convert.ToDateTime(row["ModifiedDate"]),
                ModifiedBy = row["ModifiedBy"] == DBNull.Value ? null : row["ModifiedBy"].ToString(),
                IsDeleted = Convert.ToBoolean(row["IsDeleted"]),
                DeletedDate = row["DeletedDate"] == DBNull.Value ? null : Convert.ToDateTime(row["DeletedDate"]),
                DeletedBy = row["DeletedBy"] == DBNull.Value ? null : row["DeletedBy"].ToString()
            };
        }

        /// <summary>
        /// Logs audit trail
        /// </summary>
        private async Task LogAuditAsync(string tableName, int recordId, string action, string details, string changedBy)
        {
            var sql = @"
                INSERT INTO AuditLog (TableName, RecordID, Action, NewValues, ChangedBy)
                VALUES (@TableName, @RecordID, @Action, @Details, @ChangedBy)";

            var parameters = new[]
            {
                new SqlParameter("@TableName", tableName),
                new SqlParameter("@RecordID", recordId),
                new SqlParameter("@Action", action),
                new SqlParameter("@Details", details),
                new SqlParameter("@ChangedBy", changedBy)
            };

            await DatabaseConnection.ExecuteNonQueryAsync(sql, parameters);
        }

        #endregion
    }

    /// <summary>
    /// فلاتر البحث في المبيعات - Sales search filters
    /// </summary>
    public class SalesSearchFilters
    {
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string? ProductName { get; set; }
        public string? CustomerName { get; set; }
        public string? CreatedBy { get; set; }
        public decimal? MinAmount { get; set; }
        public decimal? MaxAmount { get; set; }
    }

    /// <summary>
    /// ملخص المبيعات اليومية - Daily sales summary
    /// </summary>
    public class DailySalesSummary
    {
        public DateTime Date { get; set; }
        public int TotalTransactions { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal AverageTransaction { get; set; }
        public decimal MinTransaction { get; set; }
        public decimal MaxTransaction { get; set; }
        public int UniqueCustomers { get; set; }
        public List<ProductSummary> TopProducts { get; set; } = new List<ProductSummary>();

        public string TotalRevenueFormatted => $"{TotalRevenue:N2} ر.س";
        public string AverageTransactionFormatted => $"{AverageTransaction:N2} ر.س";
        public string DateArabic => Date.ToString("dd/MM/yyyy", new System.Globalization.CultureInfo("ar-SA"));
    }

    /// <summary>
    /// ملخص المنتج - Product summary
    /// </summary>
    public class ProductSummary
    {
        public string ProductName { get; set; } = string.Empty;
        public int TotalQuantity { get; set; }
        public decimal TotalRevenue { get; set; }
        public int TransactionCount { get; set; }
        public string TotalRevenueFormatted => $"{TotalRevenue:N2} ر.س";
    }
}
