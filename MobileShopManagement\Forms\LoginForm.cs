using MobileShopManagement.BusinessLogic;
using MobileShopManagement.Properties;
using System.Configuration;

namespace MobileShopManagement.Forms
{
    /// <summary>
    /// نموذج تسجيل الدخول - Login form
    /// Handles user authentication and login process
    /// </summary>
    public partial class LoginForm : Form
    {
        private readonly UserManager _userManager;
        private int _loginAttempts = 0;
        private readonly int _maxLoginAttempts;

        /// <summary>
        /// Constructor
        /// </summary>
        public LoginForm()
        {
            InitializeComponent();
            _userManager = new UserManager();
            
            // Get max login attempts from config
            var maxAttempts = ConfigurationManager.AppSettings["MaxLoginAttempts"];
            _maxLoginAttempts = int.TryParse(maxAttempts, out var attempts) ? attempts : 3;
            
            SetupForm();
        }

        /// <summary>
        /// Sets up the form with Arabic RTL support and styling
        /// </summary>
        private void SetupForm()
        {
            // Set RTL support
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // Set form properties
            this.Text = Resources.LoginTitle;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Size = new Size(400, 300);
            this.BackColor = Color.FromArgb(240, 248, 255);

            // Set font
            var fontName = ConfigurationManager.AppSettings["DefaultFont"] ?? "Tahoma";
            var fontSize = float.TryParse(ConfigurationManager.AppSettings["DefaultFontSize"], out var size) ? size : 9f;
            this.Font = new Font(fontName, fontSize);

            // Create and position controls
            CreateControls();
            
            // Set tab order
            txtUsername.TabIndex = 0;
            txtPassword.TabIndex = 1;
            btnLogin.TabIndex = 2;
            btnExit.TabIndex = 3;

            // Set default button
            this.AcceptButton = btnLogin;
            this.CancelButton = btnExit;

            // Handle events
            this.Load += LoginForm_Load;
            this.KeyDown += LoginForm_KeyDown;
            btnLogin.Click += BtnLogin_Click;
            btnExit.Click += BtnExit_Click;
            txtPassword.KeyPress += TxtPassword_KeyPress;
        }

        /// <summary>
        /// Creates and positions form controls
        /// </summary>
        private void CreateControls()
        {
            // Title label
            var lblTitle = new Label
            {
                Text = Resources.AppTitle,
                Font = new Font(this.Font.FontFamily, 14, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 118, 210),
                TextAlign = ContentAlignment.MiddleCenter,
                Location = new Point(20, 20),
                Size = new Size(340, 40),
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right
            };
            this.Controls.Add(lblTitle);

            // Username label
            var lblUsername = new Label
            {
                Text = Resources.Username + ":",
                Location = new Point(280, 80),
                Size = new Size(100, 23),
                TextAlign = ContentAlignment.MiddleRight
            };
            this.Controls.Add(lblUsername);

            // Username textbox
            txtUsername = new TextBox
            {
                Location = new Point(50, 80),
                Size = new Size(220, 23),
                Font = new Font(this.Font.FontFamily, 10),
                TextAlign = HorizontalAlignment.Right
            };
            this.Controls.Add(txtUsername);

            // Password label
            var lblPassword = new Label
            {
                Text = Resources.Password + ":",
                Location = new Point(280, 120),
                Size = new Size(100, 23),
                TextAlign = ContentAlignment.MiddleRight
            };
            this.Controls.Add(lblPassword);

            // Password textbox
            txtPassword = new TextBox
            {
                Location = new Point(50, 120),
                Size = new Size(220, 23),
                Font = new Font(this.Font.FontFamily, 10),
                UseSystemPasswordChar = true,
                TextAlign = HorizontalAlignment.Right
            };
            this.Controls.Add(txtPassword);

            // Login button
            btnLogin = new Button
            {
                Text = Resources.Login,
                Location = new Point(200, 170),
                Size = new Size(100, 35),
                BackColor = Color.FromArgb(76, 175, 80),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font(this.Font.FontFamily, 10, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            btnLogin.FlatAppearance.BorderSize = 0;
            this.Controls.Add(btnLogin);

            // Exit button
            btnExit = new Button
            {
                Text = Resources.Exit,
                Location = new Point(80, 170),
                Size = new Size(100, 35),
                BackColor = Color.FromArgb(244, 67, 54),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font(this.Font.FontFamily, 10, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            btnExit.FlatAppearance.BorderSize = 0;
            this.Controls.Add(btnExit);

            // Status label
            lblStatus = new Label
            {
                Text = "",
                Location = new Point(20, 220),
                Size = new Size(340, 20),
                TextAlign = ContentAlignment.MiddleCenter,
                ForeColor = Color.Red,
                Font = new Font(this.Font.FontFamily, 8)
            };
            this.Controls.Add(lblStatus);
        }

        /// <summary>
        /// Form load event handler
        /// </summary>
        private void LoginForm_Load(object? sender, EventArgs e)
        {
            // Focus on username textbox
            txtUsername.Focus();
            
            // Clear status
            lblStatus.Text = "";

            // Show welcome message for first run
            ShowWelcomeMessage();
        }

        /// <summary>
        /// Shows welcome message with default credentials
        /// </summary>
        private void ShowWelcomeMessage()
        {
            var firstRunFlag = Path.Combine(Application.StartupPath, "first_run.flag");
            if (!File.Exists(firstRunFlag))
            {
                var message = "مرحباً بك في نظام إدارة متجر الهواتف المحمولة!\n\n" +
                             "بيانات تسجيل الدخول الافتراضية:\n\n" +
                             "المدير:\n" +
                             "اسم المستخدم: admin\n" +
                             "كلمة المرور: admin123\n\n" +
                             "الكاشير:\n" +
                             "اسم المستخدم: cashier\n" +
                             "كلمة المرور: cashier123";

                MessageBox.Show(message, "مرحباً", MessageBoxButtons.OK, MessageBoxIcon.Information,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);

                // Create first run flag
                try
                {
                    File.WriteAllText(firstRunFlag, DateTime.Now.ToString());
                }
                catch { }
            }
        }

        /// <summary>
        /// Login button click event handler
        /// </summary>
        private async void BtnLogin_Click(object? sender, EventArgs e)
        {
            await PerformLogin();
        }

        /// <summary>
        /// Exit button click event handler
        /// </summary>
        private void BtnExit_Click(object? sender, EventArgs e)
        {
            Application.Exit();
        }

        /// <summary>
        /// Password textbox key press event handler
        /// </summary>
        private async void TxtPassword_KeyPress(object? sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                e.Handled = true;
                await PerformLogin();
            }
        }

        /// <summary>
        /// Form key down event handler
        /// </summary>
        private void LoginForm_KeyDown(object? sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Escape)
            {
                Application.Exit();
            }
        }

        /// <summary>
        /// Performs the login process
        /// </summary>
        private async Task PerformLogin()
        {
            try
            {
                // Validate input
                if (string.IsNullOrWhiteSpace(txtUsername.Text))
                {
                    ShowStatus("يرجى إدخال اسم المستخدم", true);
                    txtUsername.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtPassword.Text))
                {
                    ShowStatus("يرجى إدخال كلمة المرور", true);
                    txtPassword.Focus();
                    return;
                }

                // Check max attempts
                if (_loginAttempts >= _maxLoginAttempts)
                {
                    ShowStatus($"تم تجاوز الحد الأقصى لمحاولات تسجيل الدخول ({_maxLoginAttempts})", true);
                    btnLogin.Enabled = false;
                    return;
                }

                // Show loading
                ShowStatus("جاري التحقق من البيانات...", false);
                btnLogin.Enabled = false;
                this.Cursor = Cursors.WaitCursor;

                // Attempt authentication
                var user = await _userManager.AuthenticateUserAsync(txtUsername.Text.Trim(), txtPassword.Text);

                if (user != null)
                {
                    // Login successful
                    SessionManager.Login(user);
                    ShowStatus("تم تسجيل الدخول بنجاح", false);

                    // Hide login form and show main form
                    this.Hide();
                    var mainForm = new MainForm();
                    mainForm.FormClosed += (s, e) => Application.Exit();
                    mainForm.Show();
                }
                else
                {
                    // Login failed
                    _loginAttempts++;
                    var remainingAttempts = _maxLoginAttempts - _loginAttempts;
                    
                    if (remainingAttempts > 0)
                    {
                        ShowStatus($"اسم المستخدم أو كلمة المرور غير صحيحة. المحاولات المتبقية: {remainingAttempts}", true);
                    }
                    else
                    {
                        ShowStatus("تم تجاوز الحد الأقصى لمحاولات تسجيل الدخول", true);
                        btnLogin.Enabled = false;
                    }

                    // Clear password
                    txtPassword.Clear();
                    txtPassword.Focus();
                }
            }
            catch (Exception ex)
            {
                ShowStatus($"خطأ في تسجيل الدخول: {ex.Message}", true);
            }
            finally
            {
                btnLogin.Enabled = _loginAttempts < _maxLoginAttempts;
                this.Cursor = Cursors.Default;
            }
        }

        /// <summary>
        /// Shows status message
        /// </summary>
        /// <param name="message">Message to show</param>
        /// <param name="isError">Whether this is an error message</param>
        private void ShowStatus(string message, bool isError)
        {
            lblStatus.Text = message;
            lblStatus.ForeColor = isError ? Color.Red : Color.Green;
        }

        #region Form Controls
        private TextBox txtUsername = null!;
        private TextBox txtPassword = null!;
        private Button btnLogin = null!;
        private Button btnExit = null!;
        private Label lblStatus = null!;
        #endregion
    }
}
