using MobileShopManagement.BusinessLogic;
using MobileShopManagement.DataAccess;
using MobileShopManagement.Forms;
using MobileShopManagement.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System.Globalization;
using Serilog;

namespace MobileShopManagement;

/// <summary>
/// نقطة دخول التطبيق الرئيسية - Main application entry point for .NET 9
/// </summary>
internal static class Program
{
    private static IHost? _host;
    private static ILogger<object>? _logger;

    /// <summary>
    /// The main entry point for the application.
    /// </summary>
    [STAThread]
    static async Task Main()
    {
        try
        {
            // Build and configure host
            _host = CreateHostBuilder().Build();

            // Initialize services
            await InitializeServicesAsync();

            // Initialize application
            InitializeApplication();

            // Test database connection
            if (!await TestDatabaseConnectionAsync())
            {
                ShowDatabaseError();
                return;
            }

            // Start session monitoring
            SessionManager.StartSessionMonitoring();

            // Show login form
            Application.Run(new LoginForm());
        }
        catch (Exception ex)
        {
            ShowFatalError(ex);
        }
        finally
        {
            await DisposeHostAsync();
        }
    }

    /// <summary>
    /// Creates and configures the host builder with .NET 9 features
    /// </summary>
    private static IHostBuilder CreateHostBuilder()
    {
        return Host.CreateDefaultBuilder()
            .ConfigureAppConfiguration((context, config) =>
            {
                config.Sources.Clear();
                config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                      .AddJsonFile($"appsettings.{context.HostingEnvironment.EnvironmentName}.json", optional: true, reloadOnChange: true)
                      .AddEnvironmentVariables("MOBILESHOP_")
                      .AddUserSecrets<ConfigurationService>(optional: true);
            })
            .ConfigureServices((context, services) =>
            {
                // Register configuration
                services.AddSingleton(context.Configuration);

                // Register application services
                services.AddSingleton<ConfigurationService>();
                services.AddScoped<UserManager>();
                services.AddScoped<SalesManager>();
                services.AddSingleton<SessionManager>();

                // Register database services
                services.AddScoped<DatabaseConnection>();

                // Add memory cache
                services.AddMemoryCache();

                // Add HTTP client if needed for future features
                services.AddHttpClient();
            })
            .UseSerilog((context, configuration) =>
            {
                configuration.ReadFrom.Configuration(context.Configuration);
            });
    }

        /// <summary>
        /// Initializes services asynchronously
        /// </summary>
        private static async Task InitializeServicesAsync()
        {
            try
            {
                // Start the host
                await _host!.StartAsync();

                // Initialize configuration service
                ConfigurationService.Initialize(_host.Services);

                // Get logger
                _logger = _host.Services.GetRequiredService<ILogger<object>>();

                // Validate configuration
                var configuration = _host.Services.GetRequiredService<IConfiguration>();
                configuration.ValidateConfiguration();

                // Ensure directories exist
                configuration.EnsureDirectoriesExist();

                _logger.LogInformation("Services initialized successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to initialize services: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Initializes the application with proper settings using .NET 9 features
        /// </summary>
        private static void InitializeApplication()
        {
            // To customize application configuration such as set high DPI settings or default font,
            // see https://aka.ms/applicationconfiguration.
            ApplicationConfiguration.Initialize();

            // Set Arabic culture
            var arabicCulture = new CultureInfo("ar-SA");
            Thread.CurrentThread.CurrentCulture = arabicCulture;
            Thread.CurrentThread.CurrentUICulture = arabicCulture;
            CultureInfo.DefaultThreadCurrentCulture = arabicCulture;
            CultureInfo.DefaultThreadCurrentUICulture = arabicCulture;

            // Get UI settings from configuration
            var config = ConfigurationService.Instance;
            var uiSettings = config.UI;

            // Set application-wide font
            Application.SetDefaultFont(new Font(uiSettings.DefaultFont, uiSettings.DefaultFontSize));

            // Enable visual styles
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            // Set application title
            Application.SetApplicationTitle(config.Application.ApplicationTitle);

            // Handle unhandled exceptions
            Application.SetUnhandledExceptionMode(UnhandledExceptionMode.CatchException);
            Application.ThreadException += Application_ThreadException;
            AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;

            _logger?.LogInformation("Application initialized with Arabic culture and .NET 9 features");
        }

        /// <summary>
        /// Tests database connection on startup using .NET 9 async patterns
        /// </summary>
        /// <returns>True if connection successful</returns>
        private static async Task<bool> TestDatabaseConnectionAsync()
        {
            try
            {
                using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(10));
                var result = await DatabaseConnection.TestConnectionAsync();

                _logger?.LogInformation("Database connection test: {Result}", result ? "Success" : "Failed");
                return result;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Database connection test failed");
                return false;
            }
        }

        /// <summary>
        /// Disposes the host gracefully
        /// </summary>
        private static async Task DisposeHostAsync()
        {
            if (_host != null)
            {
                try
                {
                    await _host.StopAsync(TimeSpan.FromSeconds(5));
                    _host.Dispose();
                    _logger?.LogInformation("Host disposed successfully");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error disposing host: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Shows database connection error
        /// </summary>
        private static void ShowDatabaseError()
        {
            var message = "فشل في الاتصال بقاعدة البيانات.\n\n" +
                         "تأكد من:\n" +
                         "• تثبيت SQL Server LocalDB أو SQL Server Express\n" +
                         "• صحة سلسلة الاتصال في ملف App.config\n" +
                         "• تشغيل خدمة قاعدة البيانات\n\n" +
                         "سيتم إغلاق التطبيق الآن.";

            MessageBox.Show(message, "خطأ في قاعدة البيانات", 
                MessageBoxButtons.OK, MessageBoxIcon.Error, 
                MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
        }

        /// <summary>
        /// Shows fatal error message
        /// </summary>
        /// <param name="ex">Exception that occurred</param>
        private static void ShowFatalError(Exception ex)
        {
            var message = $"حدث خطأ غير متوقع:\n\n{ex.Message}\n\n" +
                         "سيتم إغلاق التطبيق الآن.\n\n" +
                         "إذا استمر هذا الخطأ، يرجى الاتصال بالدعم الفني.";

            MessageBox.Show(message, "خطأ فادح", 
                MessageBoxButtons.OK, MessageBoxIcon.Error, 
                MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);

            // Log the error (in a real application, you would log to a file or database)
            LogError(ex);
        }

        /// <summary>
        /// Handles unhandled thread exceptions
        /// </summary>
        private static void Application_ThreadException(object sender, ThreadExceptionEventArgs e)
        {
            HandleUnhandledException(e.Exception);
        }

        /// <summary>
        /// Handles unhandled domain exceptions
        /// </summary>
        private static void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            if (e.ExceptionObject is Exception ex)
            {
                HandleUnhandledException(ex);
            }
        }

        /// <summary>
        /// Handles unhandled exceptions
        /// </summary>
        /// <param name="ex">Exception to handle</param>
        private static void HandleUnhandledException(Exception ex)
        {
            try
            {
                LogError(ex);

                var message = "حدث خطأ غير متوقع في التطبيق.\n\n" +
                             $"تفاصيل الخطأ: {ex.Message}\n\n" +
                             "هل تريد المتابعة؟ (اختر 'لا' لإغلاق التطبيق)";

                var result = MessageBox.Show(message, "خطأ في التطبيق", 
                    MessageBoxButtons.YesNo, MessageBoxIcon.Warning, 
                    MessageBoxDefaultButton.Button2, MessageBoxOptions.RtlReading);

                if (result == DialogResult.No)
                {
                    Application.Exit();
                }
            }
            catch
            {
                // If we can't even show the error message, just exit
                Application.Exit();
            }
        }

        /// <summary>
        /// Logs error to file (simple implementation)
        /// </summary>
        /// <param name="ex">Exception to log</param>
        private static void LogError(Exception ex)
        {
            try
            {
                var logDirectory = Path.Combine(Application.StartupPath, "Logs");
                if (!Directory.Exists(logDirectory))
                {
                    Directory.CreateDirectory(logDirectory);
                }

                var logFile = Path.Combine(logDirectory, $"Error_{DateTime.Now:yyyyMMdd}.log");
                var logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] ERROR: {ex.Message}\n" +
                              $"Stack Trace: {ex.StackTrace}\n" +
                              $"Inner Exception: {ex.InnerException?.Message}\n" +
                              new string('-', 80) + "\n";

                File.AppendAllText(logFile, logEntry);
            }
            catch
            {
                // If we can't log, just ignore
            }
        }

        /// <summary>
        /// Creates necessary directories for the application
        /// </summary>
        private static void CreateApplicationDirectories()
        {
            try
            {
                var directories = new[]
                {
                    ConfigurationManager.AppSettings["BackupDirectory"] ?? @"C:\MobileShopBackups",
                    ConfigurationManager.AppSettings["ReportsDirectory"] ?? @"C:\MobileShopReports",
                    ConfigurationManager.AppSettings["ExportsDirectory"] ?? @"C:\MobileShopExports",
                    Path.Combine(Application.StartupPath, "Logs"),
                    Path.Combine(Application.StartupPath, "Templates")
                };

                foreach (var directory in directories)
                {
                    if (!Directory.Exists(directory))
                    {
                        Directory.CreateDirectory(directory);
                    }
                }
            }
            catch
            {
                // If we can't create directories, the application will handle it later
            }
        }

        /// <summary>
        /// Checks if this is the first run of the application
        /// </summary>
        /// <returns>True if first run</returns>
        private static bool IsFirstRun()
        {
            var settingsFile = Path.Combine(Application.StartupPath, "first_run.flag");
            return !File.Exists(settingsFile);
        }

        /// <summary>
        /// Marks the application as having been run before
        /// </summary>
        private static void MarkFirstRunComplete()
        {
            try
            {
                var settingsFile = Path.Combine(Application.StartupPath, "first_run.flag");
                File.WriteAllText(settingsFile, DateTime.Now.ToString());
            }
            catch
            {
                // If we can't create the flag file, just ignore
            }
        }

        /// <summary>
        /// Shows first run setup if needed
        /// </summary>
        private static void ShowFirstRunSetup()
        {
            if (IsFirstRun())
            {
                var message = "مرحباً بك في نظام إدارة متجر الهواتف المحمولة!\n\n" +
                             "هذا هو التشغيل الأول للبرنامج.\n" +
                             "سيتم إنشاء المجلدات المطلوبة وإعداد قاعدة البيانات.\n\n" +
                             "بيانات تسجيل الدخول الافتراضية:\n" +
                             "المدير: admin / admin123\n" +
                             "الكاشير: cashier / cashier123";

                MessageBox.Show(message, "الإعداد الأولي", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information, 
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);

                CreateApplicationDirectories();
                MarkFirstRunComplete();
            }
        }
    }
}
