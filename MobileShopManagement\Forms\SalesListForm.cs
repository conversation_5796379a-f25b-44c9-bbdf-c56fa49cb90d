using MobileShopManagement.BusinessLogic;
using MobileShopManagement.Models;
using MobileShopManagement.Properties;
using System.Configuration;

namespace MobileShopManagement.Forms
{
    /// <summary>
    /// نموذج قائمة المبيعات - Sales list form
    /// Form for viewing and managing sales transactions
    /// </summary>
    public partial class SalesListForm : Form
    {
        private readonly SalesManager _salesManager;
        private List<Sale> _currentSales;

        /// <summary>
        /// Constructor
        /// </summary>
        public SalesListForm()
        {
            InitializeComponent();
            _salesManager = new SalesManager();
            _currentSales = new List<Sale>();
            SetupForm();
        }

        /// <summary>
        /// Sets up the form with Arabic RTL support and styling
        /// </summary>
        private void SetupForm()
        {
            // Set RTL support
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // Set form properties
            this.Text = "قائمة المبيعات";
            this.WindowState = FormWindowState.Maximized;
            this.BackColor = Color.FromArgb(250, 250, 250);

            // Set font
            var fontName = ConfigurationManager.AppSettings["DefaultFont"] ?? "Tahoma";
            var fontSize = float.TryParse(ConfigurationManager.AppSettings["DefaultFontSize"], out var size) ? size : 9f;
            this.Font = new Font(fontName, fontSize);

            CreateControls();
            SetupEventHandlers();
            LoadSalesData();
        }

        /// <summary>
        /// Creates and positions form controls
        /// </summary>
        private void CreateControls()
        {
            // Create filter panel
            CreateFilterPanel();
            
            // Create data grid
            CreateDataGrid();
            
            // Create buttons panel
            CreateButtonsPanel();
        }

        /// <summary>
        /// Creates the filter panel
        /// </summary>
        private void CreateFilterPanel()
        {
            var filterPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 120,
                BackColor = Color.FromArgb(240, 240, 240),
                Padding = new Padding(10)
            };

            // Title
            var lblTitle = new Label
            {
                Text = "تصفية المبيعات",
                Font = new Font(this.Font.FontFamily, 12, FontStyle.Bold),
                Location = new Point(10, 10),
                Size = new Size(200, 25)
            };
            filterPanel.Controls.Add(lblTitle);

            // Start Date
            var lblStartDate = new Label
            {
                Text = Resources.StartDate + ":",
                Location = new Point(850, 45),
                Size = new Size(80, 23),
                TextAlign = ContentAlignment.MiddleRight
            };
            filterPanel.Controls.Add(lblStartDate);

            dtpStartDate = new DateTimePicker
            {
                Location = new Point(650, 45),
                Size = new Size(180, 23),
                Format = DateTimePickerFormat.Short,
                Value = DateTime.Today.AddDays(-30)
            };
            filterPanel.Controls.Add(dtpStartDate);

            // End Date
            var lblEndDate = new Label
            {
                Text = Resources.EndDate + ":",
                Location = new Point(550, 45),
                Size = new Size(80, 23),
                TextAlign = ContentAlignment.MiddleRight
            };
            filterPanel.Controls.Add(lblEndDate);

            dtpEndDate = new DateTimePicker
            {
                Location = new Point(350, 45),
                Size = new Size(180, 23),
                Format = DateTimePickerFormat.Short,
                Value = DateTime.Today
            };
            filterPanel.Controls.Add(dtpEndDate);

            // Product Name
            var lblProductName = new Label
            {
                Text = Resources.ProductName + ":",
                Location = new Point(850, 80),
                Size = new Size(80, 23),
                TextAlign = ContentAlignment.MiddleRight
            };
            filterPanel.Controls.Add(lblProductName);

            txtProductNameFilter = new TextBox
            {
                Location = new Point(650, 80),
                Size = new Size(180, 23),
                TextAlign = HorizontalAlignment.Right,
                PlaceholderText = "البحث في أسماء المنتجات"
            };
            filterPanel.Controls.Add(txtProductNameFilter);

            // Customer Name
            var lblCustomerName = new Label
            {
                Text = Resources.CustomerName + ":",
                Location = new Point(550, 80),
                Size = new Size(80, 23),
                TextAlign = ContentAlignment.MiddleRight
            };
            filterPanel.Controls.Add(lblCustomerName);

            txtCustomerNameFilter = new TextBox
            {
                Location = new Point(350, 80),
                Size = new Size(180, 23),
                TextAlign = HorizontalAlignment.Right,
                PlaceholderText = "البحث في أسماء العملاء"
            };
            filterPanel.Controls.Add(txtCustomerNameFilter);

            // Filter buttons
            btnFilter = new Button
            {
                Text = Resources.Search,
                Location = new Point(250, 60),
                Size = new Size(80, 35),
                BackColor = Color.FromArgb(33, 150, 243),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            btnFilter.FlatAppearance.BorderSize = 0;
            filterPanel.Controls.Add(btnFilter);

            btnClearFilter = new Button
            {
                Text = Resources.ClearFilters,
                Location = new Point(150, 60),
                Size = new Size(80, 35),
                BackColor = Color.FromArgb(158, 158, 158),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            btnClearFilter.FlatAppearance.BorderSize = 0;
            filterPanel.Controls.Add(btnClearFilter);

            this.Controls.Add(filterPanel);
        }

        /// <summary>
        /// Creates the data grid
        /// </summary>
        private void CreateDataGrid()
        {
            dgvSales = new DataGridView
            {
                Dock = DockStyle.Fill,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal,
                ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.None,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                ReadOnly = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                AllowUserToResizeRows = false,
                RowHeadersVisible = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                Font = new Font(this.Font.FontFamily, 9)
            };

            // Style headers
            dgvSales.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(63, 81, 181);
            dgvSales.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvSales.ColumnHeadersDefaultCellStyle.Font = new Font(this.Font.FontFamily, 10, FontStyle.Bold);
            dgvSales.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgvSales.ColumnHeadersHeight = 35;

            // Style rows
            dgvSales.DefaultCellStyle.BackColor = Color.White;
            dgvSales.DefaultCellStyle.ForeColor = Color.Black;
            dgvSales.DefaultCellStyle.SelectionBackColor = Color.FromArgb(200, 230, 255);
            dgvSales.DefaultCellStyle.SelectionForeColor = Color.Black;
            dgvSales.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgvSales.RowTemplate.Height = 30;

            // Alternating row colors
            dgvSales.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(248, 248, 248);

            SetupDataGridColumns();
            this.Controls.Add(dgvSales);
        }

        /// <summary>
        /// Sets up data grid columns
        /// </summary>
        private void SetupDataGridColumns()
        {
            dgvSales.Columns.Clear();

            dgvSales.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "SaleID",
                HeaderText = "رقم البيع",
                DataPropertyName = "SaleID",
                Width = 80,
                DefaultCellStyle = { Alignment = DataGridViewContentAlignment.MiddleCenter }
            });

            dgvSales.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "ProductName",
                HeaderText = Resources.ProductName,
                DataPropertyName = "ProductName",
                Width = 200,
                DefaultCellStyle = { Alignment = DataGridViewContentAlignment.MiddleRight }
            });

            dgvSales.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "SalePrice",
                HeaderText = Resources.SalePrice,
                DataPropertyName = "SalePriceFormatted",
                Width = 100,
                DefaultCellStyle = { Alignment = DataGridViewContentAlignment.MiddleCenter }
            });

            dgvSales.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Quantity",
                HeaderText = Resources.Quantity,
                DataPropertyName = "QuantitySold",
                Width = 80,
                DefaultCellStyle = { Alignment = DataGridViewContentAlignment.MiddleCenter }
            });

            dgvSales.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "TotalAmount",
                HeaderText = Resources.TotalAmount,
                DataPropertyName = "TotalAmountFormatted",
                Width = 120,
                DefaultCellStyle = { Alignment = DataGridViewContentAlignment.MiddleCenter, Font = new Font(this.Font.FontFamily, 9, FontStyle.Bold) }
            });

            dgvSales.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CustomerName",
                HeaderText = Resources.CustomerName,
                DataPropertyName = "CustomerName",
                Width = 150,
                DefaultCellStyle = { Alignment = DataGridViewContentAlignment.MiddleRight }
            });

            dgvSales.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "SaleDate",
                HeaderText = Resources.SaleDate,
                DataPropertyName = "SaleDateArabic",
                Width = 100,
                DefaultCellStyle = { Alignment = DataGridViewContentAlignment.MiddleCenter }
            });

            dgvSales.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "SaleDay",
                HeaderText = "اليوم",
                DataPropertyName = "SaleDayArabic",
                Width = 80,
                DefaultCellStyle = { Alignment = DataGridViewContentAlignment.MiddleCenter }
            });

            dgvSales.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CreatedBy",
                HeaderText = Resources.CreatedBy,
                DataPropertyName = "CreatedBy",
                Width = 100,
                DefaultCellStyle = { Alignment = DataGridViewContentAlignment.MiddleCenter }
            });
        }

        /// <summary>
        /// Creates the buttons panel
        /// </summary>
        private void CreateButtonsPanel()
        {
            var buttonsPanel = new Panel
            {
                Dock = DockStyle.Bottom,
                Height = 60,
                BackColor = Color.FromArgb(240, 240, 240),
                Padding = new Padding(10)
            };

            // Add Sale button
            btnAddSale = new Button
            {
                Text = Resources.AddSale,
                Location = new Point(10, 15),
                Size = new Size(120, 35),
                BackColor = Color.FromArgb(76, 175, 80),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font(this.Font.FontFamily, 9, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            btnAddSale.FlatAppearance.BorderSize = 0;
            buttonsPanel.Controls.Add(btnAddSale);

            // Edit Sale button
            btnEditSale = new Button
            {
                Text = Resources.Edit,
                Location = new Point(140, 15),
                Size = new Size(100, 35),
                BackColor = Color.FromArgb(255, 152, 0),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font(this.Font.FontFamily, 9, FontStyle.Bold),
                Cursor = Cursors.Hand,
                Enabled = false
            };
            btnEditSale.FlatAppearance.BorderSize = 0;
            buttonsPanel.Controls.Add(btnEditSale);

            // Delete Sale button (Admin only)
            btnDeleteSale = new Button
            {
                Text = Resources.Delete,
                Location = new Point(250, 15),
                Size = new Size(100, 35),
                BackColor = Color.FromArgb(244, 67, 54),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font(this.Font.FontFamily, 9, FontStyle.Bold),
                Cursor = Cursors.Hand,
                Enabled = false,
                Visible = SessionManager.IsAdmin
            };
            btnDeleteSale.FlatAppearance.BorderSize = 0;
            buttonsPanel.Controls.Add(btnDeleteSale);

            // Export button
            btnExport = new Button
            {
                Text = Resources.Export,
                Location = new Point(360, 15),
                Size = new Size(100, 35),
                BackColor = Color.FromArgb(33, 150, 243),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font(this.Font.FontFamily, 9, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            btnExport.FlatAppearance.BorderSize = 0;
            buttonsPanel.Controls.Add(btnExport);

            // Print button
            btnPrint = new Button
            {
                Text = Resources.Print,
                Location = new Point(470, 15),
                Size = new Size(100, 35),
                BackColor = Color.FromArgb(103, 58, 183),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font(this.Font.FontFamily, 9, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            btnPrint.FlatAppearance.BorderSize = 0;
            buttonsPanel.Controls.Add(btnPrint);

            // Close button
            btnClose = new Button
            {
                Text = Resources.Close,
                Location = new Point(580, 15),
                Size = new Size(100, 35),
                BackColor = Color.FromArgb(158, 158, 158),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font(this.Font.FontFamily, 9, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            btnClose.FlatAppearance.BorderSize = 0;
            buttonsPanel.Controls.Add(btnClose);

            // Summary label
            lblSummary = new Label
            {
                Text = "",
                Location = new Point(700, 20),
                Size = new Size(300, 25),
                TextAlign = ContentAlignment.MiddleRight,
                Font = new Font(this.Font.FontFamily, 10, FontStyle.Bold),
                ForeColor = Color.FromArgb(33, 150, 243)
            };
            buttonsPanel.Controls.Add(lblSummary);

            this.Controls.Add(buttonsPanel);
        }

        /// <summary>
        /// Sets up event handlers
        /// </summary>
        private void SetupEventHandlers()
        {
            btnFilter.Click += BtnFilter_Click;
            btnClearFilter.Click += BtnClearFilter_Click;
            btnAddSale.Click += BtnAddSale_Click;
            btnEditSale.Click += BtnEditSale_Click;
            btnDeleteSale.Click += BtnDeleteSale_Click;
            btnExport.Click += BtnExport_Click;
            btnPrint.Click += BtnPrint_Click;
            btnClose.Click += BtnClose_Click;

            dgvSales.SelectionChanged += DgvSales_SelectionChanged;
            dgvSales.CellDoubleClick += DgvSales_CellDoubleClick;

            // Filter on Enter key
            txtProductNameFilter.KeyPress += (s, e) => { if (e.KeyChar == (char)Keys.Enter) BtnFilter_Click(s, e); };
            txtCustomerNameFilter.KeyPress += (s, e) => { if (e.KeyChar == (char)Keys.Enter) BtnFilter_Click(s, e); };
        }

        /// <summary>
        /// Loads sales data
        /// </summary>
        private async void LoadSalesData()
        {
            try
            {
                var filters = new SalesSearchFilters
                {
                    StartDate = dtpStartDate.Value.Date,
                    EndDate = dtpEndDate.Value.Date,
                    ProductName = string.IsNullOrWhiteSpace(txtProductNameFilter.Text) ? null : txtProductNameFilter.Text.Trim(),
                    CustomerName = string.IsNullOrWhiteSpace(txtCustomerNameFilter.Text) ? null : txtCustomerNameFilter.Text.Trim()
                };

                _currentSales = await _salesManager.GetSalesAsync(filters);
                dgvSales.DataSource = _currentSales;

                UpdateSummary();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المبيعات: {ex.Message}", Resources.Error,
                    MessageBoxButtons.OK, MessageBoxIcon.Error,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
            }
        }

        /// <summary>
        /// Updates summary information
        /// </summary>
        private void UpdateSummary()
        {
            if (_currentSales.Any())
            {
                var totalAmount = _currentSales.Sum(s => s.TotalAmount);
                var totalTransactions = _currentSales.Count;
                lblSummary.Text = $"المعاملات: {totalTransactions} | الإجمالي: {totalAmount:N2} ر.س";
            }
            else
            {
                lblSummary.Text = "لا توجد مبيعات";
            }
        }

        #region Event Handlers

        private void BtnFilter_Click(object? sender, EventArgs e)
        {
            LoadSalesData();
        }

        private void BtnClearFilter_Click(object? sender, EventArgs e)
        {
            dtpStartDate.Value = DateTime.Today.AddDays(-30);
            dtpEndDate.Value = DateTime.Today;
            txtProductNameFilter.Clear();
            txtCustomerNameFilter.Clear();
            LoadSalesData();
        }

        private void BtnAddSale_Click(object? sender, EventArgs e)
        {
            var saleForm = new SaleForm();
            if (saleForm.ShowDialog() == DialogResult.OK)
            {
                LoadSalesData();
            }
        }

        private void BtnEditSale_Click(object? sender, EventArgs e)
        {
            if (dgvSales.SelectedRows.Count > 0)
            {
                var selectedSale = (Sale)dgvSales.SelectedRows[0].DataBoundItem;
                var saleForm = new SaleForm(selectedSale);
                if (saleForm.ShowDialog() == DialogResult.OK)
                {
                    LoadSalesData();
                }
            }
        }

        private async void BtnDeleteSale_Click(object? sender, EventArgs e)
        {
            if (dgvSales.SelectedRows.Count > 0)
            {
                var selectedSale = (Sale)dgvSales.SelectedRows[0].DataBoundItem;
                
                var result = MessageBox.Show($"هل تريد حذف البيع: {selectedSale.ProductName}؟\n\nهذا الإجراء لا يمكن التراجع عنه.",
                    "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Warning,
                    MessageBoxDefaultButton.Button2, MessageBoxOptions.RtlReading);

                if (result == DialogResult.Yes)
                {
                    try
                    {
                        var success = await _salesManager.DeleteSaleAsync(selectedSale.SaleID, SessionManager.CurrentUsername);
                        if (success)
                        {
                            MessageBox.Show(Resources.SaleDeleted, Resources.Success,
                                MessageBoxButtons.OK, MessageBoxIcon.Information,
                                MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
                            LoadSalesData();
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف البيع: {ex.Message}", Resources.Error,
                            MessageBoxButtons.OK, MessageBoxIcon.Error,
                            MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
                    }
                }
            }
        }

        private void BtnExport_Click(object? sender, EventArgs e)
        {
            // TODO: Implement export functionality
            MessageBox.Show("سيتم تنفيذ وظيفة التصدير قريباً", "قيد التطوير",
                MessageBoxButtons.OK, MessageBoxIcon.Information,
                MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
        }

        private void BtnPrint_Click(object? sender, EventArgs e)
        {
            // TODO: Implement print functionality
            MessageBox.Show("سيتم تنفيذ وظيفة الطباعة قريباً", "قيد التطوير",
                MessageBoxButtons.OK, MessageBoxIcon.Information,
                MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
        }

        private void BtnClose_Click(object? sender, EventArgs e)
        {
            this.Close();
        }

        private void DgvSales_SelectionChanged(object? sender, EventArgs e)
        {
            var hasSelection = dgvSales.SelectedRows.Count > 0;
            btnEditSale.Enabled = hasSelection;
            btnDeleteSale.Enabled = hasSelection && SessionManager.IsAdmin;
        }

        private void DgvSales_CellDoubleClick(object? sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                BtnEditSale_Click(sender, e);
            }
        }

        #endregion

        #region Form Controls
        private DateTimePicker dtpStartDate = null!;
        private DateTimePicker dtpEndDate = null!;
        private TextBox txtProductNameFilter = null!;
        private TextBox txtCustomerNameFilter = null!;
        private Button btnFilter = null!;
        private Button btnClearFilter = null!;
        private DataGridView dgvSales = null!;
        private Button btnAddSale = null!;
        private Button btnEditSale = null!;
        private Button btnDeleteSale = null!;
        private Button btnExport = null!;
        private Button btnPrint = null!;
        private Button btnClose = null!;
        private Label lblSummary = null!;
        #endregion
    }
}
