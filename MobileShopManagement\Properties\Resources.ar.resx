<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  
  <!-- Application Title -->
  <data name="AppTitle" xml:space="preserve">
    <value>نظام إدارة متجر الهواتف المحمولة</value>
  </data>
  
  <!-- Login Form -->
  <data name="LoginTitle" xml:space="preserve">
    <value>تسجيل الدخول</value>
  </data>
  <data name="Username" xml:space="preserve">
    <value>اسم المستخدم</value>
  </data>
  <data name="Password" xml:space="preserve">
    <value>كلمة المرور</value>
  </data>
  <data name="Login" xml:space="preserve">
    <value>دخول</value>
  </data>
  <data name="Exit" xml:space="preserve">
    <value>خروج</value>
  </data>
  
  <!-- Main Form -->
  <data name="MainTitle" xml:space="preserve">
    <value>نظام إدارة متجر الهواتف المحمولة</value>
  </data>
  <data name="ProductName" xml:space="preserve">
    <value>اسم المنتج</value>
  </data>
  <data name="SalePrice" xml:space="preserve">
    <value>سعر البيع</value>
  </data>
  <data name="CustomerName" xml:space="preserve">
    <value>اسم العميل</value>
  </data>
  <data name="SaleDate" xml:space="preserve">
    <value>تاريخ البيع</value>
  </data>
  <data name="Quantity" xml:space="preserve">
    <value>الكمية</value>
  </data>
  <data name="Search" xml:space="preserve">
    <value>بحث</value>
  </data>
  <data name="AddSale" xml:space="preserve">
    <value>إضافة عملية بيع</value>
  </data>
  <data name="Print" xml:space="preserve">
    <value>طباعة</value>
  </data>
  <data name="Export" xml:space="preserve">
    <value>تصدير</value>
  </data>
  <data name="Backup" xml:space="preserve">
    <value>نسخة احتياطية</value>
  </data>
  
  <!-- Additional Fields -->
  <data name="CustomerPhone" xml:space="preserve">
    <value>رقم الهاتف</value>
  </data>
  <data name="Notes" xml:space="preserve">
    <value>ملاحظات</value>
  </data>
  <data name="TotalAmount" xml:space="preserve">
    <value>المبلغ الإجمالي</value>
  </data>
  <data name="CreatedBy" xml:space="preserve">
    <value>أنشئ بواسطة</value>
  </data>
  <data name="SaleDay" xml:space="preserve">
    <value>يوم البيع</value>
  </data>
  
  <!-- Menu Items -->
  <data name="File" xml:space="preserve">
    <value>ملف</value>
  </data>
  <data name="Sales" xml:space="preserve">
    <value>المبيعات</value>
  </data>
  <data name="Reports" xml:space="preserve">
    <value>التقارير</value>
  </data>
  <data name="Users" xml:space="preserve">
    <value>المستخدمون</value>
  </data>
  <data name="Settings" xml:space="preserve">
    <value>الإعدادات</value>
  </data>
  <data name="Help" xml:space="preserve">
    <value>مساعدة</value>
  </data>
  
  <!-- Messages -->
  <data name="Success" xml:space="preserve">
    <value>نجح</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>خطأ</value>
  </data>
  <data name="Warning" xml:space="preserve">
    <value>تحذير</value>
  </data>
  <data name="Information" xml:space="preserve">
    <value>معلومات</value>
  </data>
  <data name="Confirmation" xml:space="preserve">
    <value>تأكيد</value>
  </data>
  
  <!-- Common Actions -->
  <data name="Save" xml:space="preserve">
    <value>حفظ</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>إلغاء</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>حذف</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>تعديل</value>
  </data>
  <data name="Add" xml:space="preserve">
    <value>إضافة</value>
  </data>
  <data name="Clear" xml:space="preserve">
    <value>مسح</value>
  </data>
  <data name="Close" xml:space="preserve">
    <value>إغلاق</value>
  </data>
  
  <!-- Filter and Search -->
  <data name="Filter" xml:space="preserve">
    <value>تصفية</value>
  </data>
  <data name="StartDate" xml:space="preserve">
    <value>تاريخ البداية</value>
  </data>
  <data name="EndDate" xml:space="preserve">
    <value>تاريخ النهاية</value>
  </data>
  <data name="ClearFilters" xml:space="preserve">
    <value>مسح التصفية</value>
  </data>
  
  <!-- Status Messages -->
  <data name="LoginSuccessful" xml:space="preserve">
    <value>تم تسجيل الدخول بنجاح</value>
  </data>
  <data name="LoginFailed" xml:space="preserve">
    <value>فشل في تسجيل الدخول</value>
  </data>
  <data name="SaleAdded" xml:space="preserve">
    <value>تم إضافة البيع بنجاح</value>
  </data>
  <data name="SaleUpdated" xml:space="preserve">
    <value>تم تحديث البيع بنجاح</value>
  </data>
  <data name="SaleDeleted" xml:space="preserve">
    <value>تم حذف البيع بنجاح</value>
  </data>
  <data name="BackupCreated" xml:space="preserve">
    <value>تم إنشاء النسخة الاحتياطية بنجاح</value>
  </data>
  <data name="ExportCompleted" xml:space="preserve">
    <value>تم التصدير بنجاح</value>
  </data>
  
  <!-- Validation Messages -->
  <data name="RequiredField" xml:space="preserve">
    <value>هذا الحقل مطلوب</value>
  </data>
  <data name="InvalidData" xml:space="preserve">
    <value>البيانات غير صحيحة</value>
  </data>
  <data name="InvalidPrice" xml:space="preserve">
    <value>السعر غير صحيح</value>
  </data>
  <data name="InvalidQuantity" xml:space="preserve">
    <value>الكمية غير صحيحة</value>
  </data>
  
</root>
