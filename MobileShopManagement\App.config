<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <connectionStrings>
    <!-- SQL Server LocalDB Connection String -->
    <add name="MobileShopDB" 
         connectionString="Data Source=(LocalDB)\MSSQLLocalDB;AttachDbFilename=|DataDirectory|\MobileShopDB.mdf;Integrated Security=True;Connect Timeout=30;Encrypt=False;TrustServerCertificate=False;ApplicationIntent=ReadWrite;MultiSubnetFailover=False" 
         providerName="Microsoft.Data.SqlClient" />
    
    <!-- Alternative SQL Server Express Connection String -->
    <!-- 
    <add name="MobileShopDB" 
         connectionString="Server=.\SQLEXPRESS;Database=MobileShopDB;Integrated Security=True;Connect Timeout=30;Encrypt=False;TrustServerCertificate=False;ApplicationIntent=ReadWrite;MultiSubnetFailover=False" 
         providerName="Microsoft.Data.SqlClient" />
    -->
  </connectionStrings>
  
  <appSettings>
    <!-- Application Settings -->
    <add key="ApplicationTitle" value="نظام إدارة متجر الهواتف المحمولة" />
    <add key="CompanyName" value="متجر الهواتف المحمولة" />
    <add key="DefaultLanguage" value="ar" />
    <add key="SessionTimeoutMinutes" value="30" />
    <add key="BackupDirectory" value="C:\MobileShopBackups" />
    <add key="ReportsDirectory" value="C:\MobileShopReports" />
    <add key="ExportsDirectory" value="C:\MobileShopExports" />
    
    <!-- Security Settings -->
    <add key="PasswordMinLength" value="6" />
    <add key="MaxLoginAttempts" value="3" />
    <add key="LockoutDurationMinutes" value="15" />
    
    <!-- UI Settings -->
    <add key="DefaultFont" value="Tahoma" />
    <add key="DefaultFontSize" value="9" />
    <add key="GridRowHeight" value="25" />
    
    <!-- Printing Settings -->
    <add key="ReceiptWidth" value="80" />
    <add key="PrinterName" value="" />
    <add key="PrintPreview" value="true" />
    
    <!-- Export Settings -->
    <add key="ExcelTemplateFile" value="Templates\SalesReport.xlsx" />
    <add key="MaxExportRecords" value="10000" />
  </appSettings>
  
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8" />
  </startup>
</configuration>
