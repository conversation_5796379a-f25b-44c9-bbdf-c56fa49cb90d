{"ConnectionStrings": {"MobileShopDB": "Data Source=(LocalDB)\\MSSQLLocalDB;AttachDbFilename=|DataDirectory|\\MobileShopDB.mdf;Integrated Security=True;Connect Timeout=30;Encrypt=False;TrustServerCertificate=False;ApplicationIntent=ReadWrite;MultiSubnetFailover=False;Pooling=true;Max Pool Size=100;Min Pool Size=5;Connection Timeout=30;Command Timeout=30", "MobileShopDB_SqlExpress": "Server=.\\SQLEXPRESS;Database=MobileShopDB;Integrated Security=True;Connect Timeout=30;Encrypt=False;TrustServerCertificate=False;ApplicationIntent=ReadWrite;MultiSubnetFailover=False;Pooling=true;Max Pool Size=100;Min Pool Size=5"}, "ApplicationSettings": {"ApplicationTitle": "نظام إدارة متجر الهواتف المحمولة", "CompanyName": "متجر الهواتف المحمولة", "DefaultLanguage": "ar", "SessionTimeoutMinutes": 30, "BackupDirectory": "C:\\MobileShopBackups", "ReportsDirectory": "C:\\MobileShopReports", "ExportsDirectory": "C:\\MobileShopExports", "LogsDirectory": "C:\\MobileShopLogs", "TemplatesDirectory": "Templates"}, "SecuritySettings": {"PasswordMinLength": 6, "MaxLoginAttempts": 3, "LockoutDurationMinutes": 15, "SessionTokenExpiryMinutes": 60, "EnableTwoFactorAuth": false, "RequirePasswordComplexity": true, "PasswordHistoryCount": 5, "AutoLockoutEnabled": true}, "UISettings": {"DefaultFont": "<PERSON><PERSON><PERSON>", "DefaultFontSize": 9, "GridRowHeight": 25, "Theme": "<PERSON><PERSON><PERSON>", "EnableAnimations": true, "ShowTooltips": true, "AutoSaveInterval": 300, "DefaultFormSize": {"Width": 1024, "Height": 768}}, "PrintingSettings": {"ReceiptWidth": 80, "PrinterName": "", "PrintPreview": true, "DefaultPaperSize": "A4", "PrintMargins": {"Top": 20, "Bottom": 20, "Left": 20, "Right": 20}, "AutoPrint": false}, "ExportSettings": {"ExcelTemplateFile": "Templates\\SalesReport.xlsx", "MaxExportRecords": 10000, "DefaultExportFormat": "Excel", "IncludeCharts": true, "CompressExports": false, "ExportTimeout": 300}, "DatabaseSettings": {"CommandTimeout": 30, "ConnectionTimeout": 30, "EnableRetry": true, "MaxRetryAttempts": 3, "RetryDelay": 1000, "EnableConnectionPooling": true, "BackupRetentionDays": 30, "AutoBackupEnabled": false, "AutoBackupTime": "02:00:00"}, "LoggingSettings": {"LogLevel": "Information", "EnableFileLogging": true, "EnableConsoleLogging": false, "LogRetentionDays": 30, "MaxLogFileSize": 10485760, "LogFormat": "Structured"}, "PerformanceSettings": {"EnableCaching": true, "CacheExpiryMinutes": 30, "MaxCacheSize": 100, "EnableLazyLoading": true, "BatchSize": 1000, "EnableAsyncOperations": true}, "FeatureFlags": {"EnableAdvancedReports": true, "EnableDataExport": true, "EnableUserManagement": true, "EnableBackupRestore": true, "EnableAuditLogging": true, "EnableNotifications": true, "EnableDarkMode": false, "EnableCloudSync": false}, "NotificationSettings": {"EnableEmailNotifications": false, "EnableSMSNotifications": false, "EnableDesktopNotifications": true, "NotificationSound": true, "EmailServer": "", "SMSProvider": ""}, "IntegrationSettings": {"EnableAPIAccess": false, "APIPort": 8080, "EnableWebInterface": false, "WebPort": 8081, "EnableMobileApp": false, "CloudProvider": ""}, "Serilog": {"Using": ["Serilog.Sinks.File", "Serilog.Sinks.Console"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "File", "Args": {"path": "C:\\MobileShopLogs\\log-.txt", "rollingInterval": "Day", "retainedFileCountLimit": 30, "formatter": "Serilog.Formatting.Compact.CompactJsonFormatter, Serilog.Formatting.Compact"}}, {"Name": "<PERSON><PERSON><PERSON>", "Args": {"formatter": "Serilog.Formatting.Display.MessageTemplateTextFormatter, Serilog"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]}}