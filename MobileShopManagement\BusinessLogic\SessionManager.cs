using MobileShopManagement.Models;
using System.Configuration;

namespace MobileShopManagement.BusinessLogic
{
    /// <summary>
    /// مدير الجلسات - Session manager
    /// Handles user session management and authentication state
    /// </summary>
    public static class SessionManager
    {
        private static User? _currentUser;
        private static DateTime _lastActivity;
        private static readonly int _sessionTimeoutMinutes;
        private static readonly object _lock = new object();

        /// <summary>
        /// Static constructor to initialize session settings
        /// </summary>
        static SessionManager()
        {
            var timeoutSetting = ConfigurationManager.AppSettings["SessionTimeoutMinutes"];
            _sessionTimeoutMinutes = int.TryParse(timeoutSetting, out var timeout) ? timeout : 30;
        }

        /// <summary>
        /// Current authenticated user
        /// </summary>
        public static User? CurrentUser
        {
            get
            {
                lock (_lock)
                {
                    if (_currentUser != null && IsSessionExpired())
                    {
                        Logout();
                    }
                    return _currentUser;
                }
            }
        }

        /// <summary>
        /// Checks if user is authenticated
        /// </summary>
        public static bool IsAuthenticated => CurrentUser != null;

        /// <summary>
        /// Checks if current user is admin
        /// </summary>
        public static bool IsAdmin => CurrentUser?.IsAdmin == true;

        /// <summary>
        /// Checks if current user is cashier
        /// </summary>
        public static bool IsCashier => CurrentUser?.IsCashier == true;

        /// <summary>
        /// Current username
        /// </summary>
        public static string CurrentUsername => CurrentUser?.Username ?? "غير مسجل";

        /// <summary>
        /// Current user role in Arabic
        /// </summary>
        public static string CurrentUserRoleArabic => CurrentUser?.RoleInArabic ?? "غير محدد";

        /// <summary>
        /// Session start time
        /// </summary>
        public static DateTime SessionStartTime { get; private set; }

        /// <summary>
        /// Last activity time
        /// </summary>
        public static DateTime LastActivity => _lastActivity;

        /// <summary>
        /// Time until session expires
        /// </summary>
        public static TimeSpan TimeUntilExpiry
        {
            get
            {
                if (!IsAuthenticated) return TimeSpan.Zero;
                
                var expiryTime = _lastActivity.AddMinutes(_sessionTimeoutMinutes);
                var remaining = expiryTime - DateTime.Now;
                return remaining > TimeSpan.Zero ? remaining : TimeSpan.Zero;
            }
        }

        /// <summary>
        /// Logs in a user and starts a session
        /// </summary>
        /// <param name="user">User to log in</param>
        public static void Login(User user)
        {
            if (user == null)
                throw new ArgumentNullException(nameof(user), "المستخدم لا يمكن أن يكون فارغاً");

            lock (_lock)
            {
                _currentUser = user;
                _lastActivity = DateTime.Now;
                SessionStartTime = DateTime.Now;
            }

            // Raise login event
            OnUserLoggedIn?.Invoke(user);
        }

        /// <summary>
        /// Logs out the current user and ends the session
        /// </summary>
        public static void Logout()
        {
            User? loggedOutUser;
            
            lock (_lock)
            {
                loggedOutUser = _currentUser;
                _currentUser = null;
                _lastActivity = DateTime.MinValue;
            }

            // Raise logout event
            if (loggedOutUser != null)
            {
                OnUserLoggedOut?.Invoke(loggedOutUser);
            }
        }

        /// <summary>
        /// Updates the last activity time to extend the session
        /// </summary>
        public static void UpdateActivity()
        {
            if (IsAuthenticated)
            {
                lock (_lock)
                {
                    _lastActivity = DateTime.Now;
                }
            }
        }

        /// <summary>
        /// Checks if the current session has expired
        /// </summary>
        /// <returns>True if session is expired</returns>
        public static bool IsSessionExpired()
        {
            if (!IsAuthenticated) return true;
            
            return DateTime.Now > _lastActivity.AddMinutes(_sessionTimeoutMinutes);
        }

        /// <summary>
        /// Forces session expiry
        /// </summary>
        public static void ExpireSession()
        {
            lock (_lock)
            {
                _lastActivity = DateTime.MinValue;
            }
        }

        /// <summary>
        /// Checks if current user has permission for a specific action
        /// </summary>
        /// <param name="permission">Permission to check</param>
        /// <returns>True if user has permission</returns>
        public static bool HasPermission(UserPermission permission)
        {
            if (!IsAuthenticated) return false;

            return permission switch
            {
                UserPermission.ViewSales => true, // All authenticated users can view sales
                UserPermission.AddSale => true, // All authenticated users can add sales
                UserPermission.EditSale => IsAdmin || IsCashier, // All can edit their own sales
                UserPermission.DeleteSale => IsAdmin, // Only admin can delete
                UserPermission.ManageUsers => IsAdmin, // Only admin can manage users
                UserPermission.ViewReports => true, // All can view reports
                UserPermission.ExportData => IsAdmin || IsCashier, // All can export
                UserPermission.CreateBackup => IsAdmin, // Only admin can backup
                UserPermission.SystemSettings => IsAdmin, // Only admin can change settings
                _ => false
            };
        }

        /// <summary>
        /// Validates permission and throws exception if not authorized
        /// </summary>
        /// <param name="permission">Permission to validate</param>
        /// <exception cref="UnauthorizedAccessException">Thrown if user doesn't have permission</exception>
        public static void ValidatePermission(UserPermission permission)
        {
            if (!IsAuthenticated)
                throw new UnauthorizedAccessException("يجب تسجيل الدخول أولاً");

            if (!HasPermission(permission))
                throw new UnauthorizedAccessException("ليس لديك صلاحية للقيام بهذا الإجراء");
        }

        /// <summary>
        /// Gets session information
        /// </summary>
        /// <returns>Session information object</returns>
        public static SessionInfo GetSessionInfo()
        {
            return new SessionInfo
            {
                IsAuthenticated = IsAuthenticated,
                Username = CurrentUsername,
                Role = CurrentUser?.Role ?? "غير محدد",
                RoleArabic = CurrentUserRoleArabic,
                SessionStartTime = SessionStartTime,
                LastActivity = LastActivity,
                TimeUntilExpiry = TimeUntilExpiry,
                IsExpired = IsSessionExpired()
            };
        }

        /// <summary>
        /// Event raised when user logs in
        /// </summary>
        public static event Action<User>? OnUserLoggedIn;

        /// <summary>
        /// Event raised when user logs out
        /// </summary>
        public static event Action<User>? OnUserLoggedOut;

        /// <summary>
        /// Event raised when session expires
        /// </summary>
        public static event Action? OnSessionExpired;

        /// <summary>
        /// Starts session monitoring timer
        /// </summary>
        public static void StartSessionMonitoring()
        {
            var timer = new System.Timers.Timer(60000); // Check every minute
            timer.Elapsed += (sender, e) =>
            {
                if (IsAuthenticated && IsSessionExpired())
                {
                    OnSessionExpired?.Invoke();
                    Logout();
                }
            };
            timer.Start();
        }
    }

    /// <summary>
    /// صلاحيات المستخدم - User permissions enumeration
    /// </summary>
    public enum UserPermission
    {
        ViewSales,
        AddSale,
        EditSale,
        DeleteSale,
        ManageUsers,
        ViewReports,
        ExportData,
        CreateBackup,
        SystemSettings
    }

    /// <summary>
    /// معلومات الجلسة - Session information
    /// </summary>
    public class SessionInfo
    {
        public bool IsAuthenticated { get; set; }
        public string Username { get; set; } = string.Empty;
        public string Role { get; set; } = string.Empty;
        public string RoleArabic { get; set; } = string.Empty;
        public DateTime SessionStartTime { get; set; }
        public DateTime LastActivity { get; set; }
        public TimeSpan TimeUntilExpiry { get; set; }
        public bool IsExpired { get; set; }

        /// <summary>
        /// Session duration
        /// </summary>
        public TimeSpan SessionDuration => DateTime.Now - SessionStartTime;

        /// <summary>
        /// Session duration in Arabic format
        /// </summary>
        public string SessionDurationArabic
        {
            get
            {
                var duration = SessionDuration;
                if (duration.TotalHours >= 1)
                    return $"{duration.Hours} ساعة و {duration.Minutes} دقيقة";
                else
                    return $"{duration.Minutes} دقيقة";
            }
        }

        /// <summary>
        /// Time until expiry in Arabic format
        /// </summary>
        public string TimeUntilExpiryArabic
        {
            get
            {
                if (IsExpired) return "منتهية الصلاحية";
                
                if (TimeUntilExpiry.TotalHours >= 1)
                    return $"{TimeUntilExpiry.Hours} ساعة و {TimeUntilExpiry.Minutes} دقيقة";
                else
                    return $"{TimeUntilExpiry.Minutes} دقيقة";
            }
        }
    }
}
