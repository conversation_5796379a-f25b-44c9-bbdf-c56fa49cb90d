# نظام إدارة متجر الهواتف المحمولة
# Mobile Shop Management System

## نظرة عامة | Overview

نظام شامل لإدارة متجر الهواتف المحمولة مطور باستخدام .NET 6 و Windows Forms مع دعم كامل للغة العربية ونظام RTL.

A comprehensive mobile phone shop management system developed using .NET 6 and Windows Forms with full Arabic language support and RTL layout.

## المميزات الرئيسية | Key Features

### 🔐 الأمان والمصادقة | Security & Authentication
- تشفير كلمات المرور باستخدام BCrypt
- نظام أدوار المستخدمين (مدير/كاشير)
- إدارة الجلسات مع انتهاء صلاحية تلقائي
- تتبع محاولات تسجيل الدخول الفاشلة
- سجل مراجعة شامل لجميع العمليات

### 📱 إدارة المبيعات | Sales Management
- إضافة وتعديل وحذف المبيعات
- بحث وتصفية متقدمة
- تتبع معلومات العملاء
- حساب تلقائي للمبالغ الإجمالية
- دعم المعاملات المتعددة

### 📊 التقارير والإحصائيات | Reports & Analytics
- تقارير يومية مفصلة
- إحصائيات المبيعات الفورية
- أفضل المنتجات مبيعاً
- تحليل أداء العملاء
- رسوم بيانية تفاعلية

### 👥 إدارة المستخدمين | User Management
- إضافة وتعديل المستخدمين (للمدير فقط)
- تغيير كلمات المرور
- إدارة الأدوار والصلاحيات
- تتبع نشاط المستخدمين

### 💾 النسخ الاحتياطي والتصدير | Backup & Export
- إنشاء نسخ احتياطية من قاعدة البيانات
- تصدير البيانات إلى Excel
- طباعة التقارير والفواتير
- استيراد وتصدير الإعدادات

### 🌐 دعم اللغة العربية | Arabic Language Support
- واجهة مستخدم باللغة العربية بالكامل
- تخطيط RTL (من اليمين إلى اليسار)
- تنسيق التواريخ والأرقام العربية
- خطوط عربية محسنة (Tahoma)

## متطلبات النظام | System Requirements

### البرمجيات المطلوبة | Required Software
- Windows 10/11 (64-bit)
- .NET 6.0 Runtime أو أحدث
- SQL Server LocalDB أو SQL Server Express
- Microsoft Visual C++ Redistributable

### مواصفات الأجهزة | Hardware Specifications
- المعالج: Intel Core i3 أو AMD equivalent
- الذاكرة: 4 GB RAM (8 GB مستحسن)
- مساحة القرص: 500 MB مساحة فارغة
- الشاشة: 1024x768 أو أعلى

## التثبيت والإعداد | Installation & Setup

### 1. تحضير البيئة | Environment Preparation

```bash
# تحميل وتثبيت .NET 6 Runtime
# Download and install .NET 6 Runtime
https://dotnet.microsoft.com/download/dotnet/6.0

# تثبيت SQL Server LocalDB
# Install SQL Server LocalDB
https://docs.microsoft.com/en-us/sql/database-engine/configure-windows/sql-server-express-localdb
```

### 2. إعداد قاعدة البيانات | Database Setup

```sql
-- تشغيل سكريبت إنشاء قاعدة البيانات
-- Run database creation script
sqlcmd -S (LocalDB)\MSSQLLocalDB -i "Database\CreateDatabase.sql"

-- إدراج البيانات النموذجية
-- Insert sample data
sqlcmd -S (LocalDB)\MSSQLLocalDB -i "Database\SampleData.sql"
```

### 3. تكوين التطبيق | Application Configuration

قم بتعديل ملف `App.config` حسب بيئتك:

```xml
<connectionStrings>
  <add name="MobileShopDB" 
       connectionString="Data Source=(LocalDB)\MSSQLLocalDB;AttachDbFilename=|DataDirectory|\MobileShopDB.mdf;Integrated Security=True" 
       providerName="Microsoft.Data.SqlClient" />
</connectionStrings>
```

### 4. تشغيل التطبيق | Running the Application

```bash
# من مجلد المشروع
# From project folder
dotnet run

# أو تشغيل الملف التنفيذي مباشرة
# Or run executable directly
MobileShopManagement.exe
```

## بيانات تسجيل الدخول الافتراضية | Default Login Credentials

### المدير | Administrator
- **اسم المستخدم | Username:** `admin`
- **كلمة المرور | Password:** `admin123`

### الكاشير | Cashier
- **اسم المستخدم | Username:** `cashier`
- **كلمة المرور | Password:** `cashier123`

## دليل الاستخدام | User Guide

### للمدير | For Administrators

1. **إدارة المستخدمين**
   - إضافة مستخدمين جدد
   - تعديل أدوار المستخدمين
   - إلغاء تفعيل الحسابات

2. **النسخ الاحتياطي**
   - إنشاء نسخ احتياطية دورية
   - استعادة البيانات
   - جدولة النسخ التلقائية

3. **التقارير المتقدمة**
   - تقارير شاملة للمبيعات
   - تحليل الأداء
   - إحصائيات مفصلة

### للكاشير | For Cashiers

1. **إدارة المبيعات**
   - إضافة مبيعات جديدة
   - تعديل المبيعات الحالية
   - البحث في السجلات

2. **خدمة العملاء**
   - تسجيل معلومات العملاء
   - تتبع المشتريات
   - طباعة الفواتير

## الهيكل التقني | Technical Architecture

### طبقات النظام | System Layers

```
┌─────────────────────────────────┐
│     Presentation Layer          │  ← Windows Forms (Arabic UI)
├─────────────────────────────────┤
│     Business Logic Layer        │  ← Managers & Services
├─────────────────────────────────┤
│     Data Access Layer           │  ← Database Operations
├─────────────────────────────────┤
│     Database Layer              │  ← SQL Server LocalDB
└─────────────────────────────────┘
```

### التقنيات المستخدمة | Technologies Used

- **Frontend:** Windows Forms with RTL support
- **Backend:** .NET 6 C#
- **Database:** SQL Server LocalDB/Express
- **Security:** BCrypt password hashing
- **Export:** ClosedXML for Excel export
- **Localization:** Resource files (.resx)

## الأمان | Security

### تشفير البيانات | Data Encryption
- كلمات المرور مشفرة باستخدام BCrypt
- اتصالات قاعدة البيانات آمنة
- تشفير البيانات الحساسة

### التحكم في الوصول | Access Control
- نظام أدوار متعدد المستويات
- صلاحيات محددة لكل دور
- تسجيل جميع العمليات

### مراجعة الأمان | Security Auditing
- سجل مراجعة شامل
- تتبع تسجيل الدخول
- مراقبة العمليات الحساسة

## النسخ الاحتياطي | Backup & Recovery

### النسخ التلقائية | Automatic Backups
```csharp
// إعداد النسخ التلقائية
BackupScheduler.ScheduleDaily(TimeSpan.FromHours(2)); // كل ساعتين
```

### الاستعادة | Recovery
```sql
-- استعادة من نسخة احتياطية
RESTORE DATABASE MobileShopDB FROM DISK = 'C:\Backups\MobileShopDB_20241214.bak'
```

## استكشاف الأخطاء | Troubleshooting

### مشاكل شائعة | Common Issues

#### 1. خطأ في الاتصال بقاعدة البيانات
```
الحل: تأكد من تشغيل SQL Server LocalDB
Solution: Ensure SQL Server LocalDB is running
```

#### 2. مشكلة في عرض النصوص العربية
```
الحل: تأكد من تثبيت خط Tahoma
Solution: Ensure Tahoma font is installed
```

#### 3. بطء في الأداء
```
الحل: قم بإعادة فهرسة قاعدة البيانات
Solution: Reindex the database
```

## التطوير والمساهمة | Development & Contributing

### إعداد بيئة التطوير | Development Environment Setup

```bash
# استنساخ المشروع
git clone https://github.com/your-repo/mobile-shop-management.git

# فتح في Visual Studio
# Open in Visual Studio
start MobileShopManagement.sln

# استعادة الحزم
# Restore packages
dotnet restore
```

### معايير الكود | Code Standards
- استخدام XML Documentation للتوثيق
- اتباع معايير C# Coding Standards
- كتابة Unit Tests للوظائف الحرجة
- دعم اللغة العربية في جميع النصوص

## الترخيص | License

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## الدعم والمساعدة | Support & Help

### التواصل | Contact
- **البريد الإلكتروني | Email:** <EMAIL>
- **الهاتف | Phone:** +966-11-1234567
- **الموقع | Website:** https://www.mobileshop.com

### الوثائق | Documentation
- [دليل المستخدم الكامل](docs/user-guide-ar.pdf)
- [دليل المطور](docs/developer-guide.md)
- [API Documentation](docs/api-reference.md)

---

**تم تطويره بـ ❤️ للمجتمع العربي | Developed with ❤️ for the Arabic community**
