using System.ComponentModel.DataAnnotations;

namespace MobileShopManagement.Models
{
    /// <summary>
    /// نموذج المبيعات - Sales model
    /// Represents a sale transaction in the mobile shop management system
    /// </summary>
    public class Sale
    {
        /// <summary>
        /// معرف البيع الفريد - Unique sale identifier
        /// </summary>
        public int SaleID { get; set; }

        /// <summary>
        /// اسم المنتج - Product name
        /// </summary>
        [Required(ErrorMessage = "اسم المنتج مطلوب")]
        [StringLength(100, ErrorMessage = "اسم المنتج يجب أن يكون أقل من 100 حرف")]
        public string ProductName { get; set; } = string.Empty;

        /// <summary>
        /// سعر البيع - Sale price
        /// </summary>
        [Required(ErrorMessage = "سعر البيع مطلوب")]
        [Range(0.01, 999999.99, ErrorMessage = "سعر البيع يجب أن يكون أكبر من صفر")]
        public decimal SalePrice { get; set; }

        /// <summary>
        /// تاريخ البيع - Sale date
        /// </summary>
        [Required(ErrorMessage = "تاريخ البيع مطلوب")]
        public DateTime SaleDate { get; set; } = DateTime.Now;

        /// <summary>
        /// يوم البيع - Sale day (computed from SaleDate)
        /// </summary>
        public string SaleDay { get; set; } = string.Empty;

        /// <summary>
        /// الكمية المباعة - Quantity sold
        /// </summary>
        [Required(ErrorMessage = "الكمية مطلوبة")]
        [Range(1, 1000, ErrorMessage = "الكمية يجب أن تكون بين 1 و 1000")]
        public int QuantitySold { get; set; } = 1;

        /// <summary>
        /// اسم العميل - Customer name (optional)
        /// </summary>
        [StringLength(100, ErrorMessage = "اسم العميل يجب أن يكون أقل من 100 حرف")]
        public string? CustomerName { get; set; }

        /// <summary>
        /// رقم هاتف العميل - Customer phone (optional)
        /// </summary>
        [StringLength(20, ErrorMessage = "رقم الهاتف يجب أن يكون أقل من 20 حرف")]
        [RegularExpression(@"^[\d\+\-\(\)\s]*$", ErrorMessage = "رقم الهاتف غير صحيح")]
        public string? CustomerPhone { get; set; }

        /// <summary>
        /// ملاحظات - Notes (optional)
        /// </summary>
        [StringLength(500, ErrorMessage = "الملاحظات يجب أن تكون أقل من 500 حرف")]
        public string? Notes { get; set; }

        /// <summary>
        /// منشئ البيع - Sale creator (username)
        /// </summary>
        [Required(ErrorMessage = "منشئ البيع مطلوب")]
        public string CreatedBy { get; set; } = string.Empty;

        /// <summary>
        /// تاريخ الإنشاء - Creation date
        /// </summary>
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// تاريخ التعديل - Modification date
        /// </summary>
        public DateTime? ModifiedDate { get; set; }

        /// <summary>
        /// معدل البيع - Sale modifier
        /// </summary>
        public string? ModifiedBy { get; set; }

        /// <summary>
        /// حالة الحذف - Soft delete flag
        /// </summary>
        public bool IsDeleted { get; set; } = false;

        /// <summary>
        /// تاريخ الحذف - Deletion date
        /// </summary>
        public DateTime? DeletedDate { get; set; }

        /// <summary>
        /// محذف البيع - Sale deleter
        /// </summary>
        public string? DeletedBy { get; set; }

        /// <summary>
        /// المبلغ الإجمالي - Total amount (calculated property)
        /// </summary>
        public decimal TotalAmount => SalePrice * QuantitySold;

        /// <summary>
        /// تاريخ البيع بالتنسيق العربي - Sale date in Arabic format
        /// </summary>
        public string SaleDateArabic
        {
            get
            {
                var culture = new System.Globalization.CultureInfo("ar-SA");
                return SaleDate.ToString("dd/MM/yyyy", culture);
            }
        }

        /// <summary>
        /// وقت البيع بالتنسيق العربي - Sale time in Arabic format
        /// </summary>
        public string SaleTimeArabic
        {
            get
            {
                var culture = new System.Globalization.CultureInfo("ar-SA");
                return SaleDate.ToString("HH:mm", culture);
            }
        }

        /// <summary>
        /// يوم البيع بالعربية - Sale day in Arabic
        /// </summary>
        public string SaleDayArabic
        {
            get
            {
                return SaleDate.DayOfWeek switch
                {
                    DayOfWeek.Sunday => "الأحد",
                    DayOfWeek.Monday => "الاثنين",
                    DayOfWeek.Tuesday => "الثلاثاء",
                    DayOfWeek.Wednesday => "الأربعاء",
                    DayOfWeek.Thursday => "الخميس",
                    DayOfWeek.Friday => "الجمعة",
                    DayOfWeek.Saturday => "السبت",
                    _ => "غير محدد"
                };
            }
        }

        /// <summary>
        /// سعر البيع مع رمز العملة - Sale price with currency symbol
        /// </summary>
        public string SalePriceFormatted => $"{SalePrice:N2} ر.س";

        /// <summary>
        /// المبلغ الإجمالي مع رمز العملة - Total amount with currency symbol
        /// </summary>
        public string TotalAmountFormatted => $"{TotalAmount:N2} ر.س";

        /// <summary>
        /// حالة البيع - Sale status
        /// </summary>
        public string Status
        {
            get
            {
                if (IsDeleted) return "محذوف";
                return "نشط";
            }
        }

        /// <summary>
        /// Default constructor
        /// </summary>
        public Sale()
        {
        }

        /// <summary>
        /// Constructor with basic sale information
        /// </summary>
        /// <param name="productName">اسم المنتج</param>
        /// <param name="salePrice">سعر البيع</param>
        /// <param name="quantity">الكمية</param>
        /// <param name="createdBy">منشئ البيع</param>
        public Sale(string productName, decimal salePrice, int quantity, string createdBy)
        {
            ProductName = productName;
            SalePrice = salePrice;
            QuantitySold = quantity;
            CreatedBy = createdBy;
            SaleDate = DateTime.Now;
            CreatedDate = DateTime.Now;
        }

        /// <summary>
        /// Validates sale data
        /// </summary>
        /// <returns>List of validation errors</returns>
        public List<string> Validate()
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(ProductName))
                errors.Add("اسم المنتج مطلوب");

            if (ProductName?.Length > 100)
                errors.Add("اسم المنتج يجب أن يكون أقل من 100 حرف");

            if (SalePrice <= 0)
                errors.Add("سعر البيع يجب أن يكون أكبر من صفر");

            if (SalePrice > 999999.99m)
                errors.Add("سعر البيع كبير جداً");

            if (QuantitySold <= 0)
                errors.Add("الكمية يجب أن تكون أكبر من صفر");

            if (QuantitySold > 1000)
                errors.Add("الكمية كبيرة جداً");

            if (string.IsNullOrWhiteSpace(CreatedBy))
                errors.Add("منشئ البيع مطلوب");

            if (!string.IsNullOrWhiteSpace(CustomerName) && CustomerName.Length > 100)
                errors.Add("اسم العميل يجب أن يكون أقل من 100 حرف");

            if (!string.IsNullOrWhiteSpace(CustomerPhone) && CustomerPhone.Length > 20)
                errors.Add("رقم الهاتف يجب أن يكون أقل من 20 حرف");

            if (!string.IsNullOrWhiteSpace(Notes) && Notes.Length > 500)
                errors.Add("الملاحظات يجب أن تكون أقل من 500 حرف");

            // Validate phone number format if provided
            if (!string.IsNullOrWhiteSpace(CustomerPhone))
            {
                var phoneRegex = new System.Text.RegularExpressions.Regex(@"^[\d\+\-\(\)\s]*$");
                if (!phoneRegex.IsMatch(CustomerPhone))
                    errors.Add("رقم الهاتف غير صحيح");
            }

            return errors;
        }

        /// <summary>
        /// Creates a copy of the sale for editing
        /// </summary>
        /// <returns>Sale copy</returns>
        public Sale Clone()
        {
            return new Sale
            {
                SaleID = SaleID,
                ProductName = ProductName,
                SalePrice = SalePrice,
                SaleDate = SaleDate,
                SaleDay = SaleDay,
                QuantitySold = QuantitySold,
                CustomerName = CustomerName,
                CustomerPhone = CustomerPhone,
                Notes = Notes,
                CreatedBy = CreatedBy,
                CreatedDate = CreatedDate,
                ModifiedDate = ModifiedDate,
                ModifiedBy = ModifiedBy,
                IsDeleted = IsDeleted,
                DeletedDate = DeletedDate,
                DeletedBy = DeletedBy
            };
        }

        /// <summary>
        /// Returns string representation of the sale
        /// </summary>
        /// <returns>Sale string representation</returns>
        public override string ToString()
        {
            return $"{ProductName} - {TotalAmountFormatted} ({SaleDateArabic})";
        }
    }
}
