using MobileShopManagement.DataAccess;
using MobileShopManagement.Models;
using Microsoft.Data.SqlClient;
using System.Data;

namespace MobileShopManagement.BusinessLogic
{
    /// <summary>
    /// مدير المستخدمين - User manager
    /// Handles all user-related business operations
    /// </summary>
    public class UserManager
    {
        /// <summary>
        /// Authenticates a user with username and password
        /// </summary>
        /// <param name="username">اسم المستخدم</param>
        /// <param name="password">كلمة المرور</param>
        /// <returns>User object if authentication successful, null otherwise</returns>
        public async Task<User?> AuthenticateUserAsync(string username, string password)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(username) || string.IsNullOrWhiteSpace(password))
                    return null;

                // Get user from database
                var user = await GetUserByUsernameAsync(username);
                if (user == null || !user.IsActive)
                    return null;

                // Check if user is locked out
                if (user.IsLockedOut)
                {
                    throw new InvalidOperationException($"الحساب مقفل حتى {user.LockoutEndTime:yyyy-MM-dd HH:mm}");
                }

                // Verify password
                if (!SecurityManager.VerifyPassword(password, user.PasswordHash))
                {
                    // Increment failed login attempts
                    await IncrementFailedLoginAttemptsAsync(username);
                    return null;
                }

                // Reset failed login attempts and update last login
                await ResetFailedLoginAttemptsAsync(username);
                await UpdateLastLoginAsync(username);

                return user.ToSafeUser();
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"فشل في تسجيل الدخول: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Creates a new user
        /// </summary>
        /// <param name="user">User object with details</param>
        /// <param name="createdBy">Username of creator</param>
        /// <returns>Created user ID</returns>
        public async Task<int> CreateUserAsync(User user, string createdBy)
        {
            try
            {
                // Validate user data
                var validationErrors = user.Validate();
                if (validationErrors.Any())
                    throw new ArgumentException($"بيانات المستخدم غير صحيحة: {string.Join(", ", validationErrors)}");

                // Check if username already exists
                var existingUser = await GetUserByUsernameAsync(user.Username);
                if (existingUser != null)
                    throw new InvalidOperationException("اسم المستخدم موجود بالفعل");

                // Hash password
                var (hashedPassword, salt) = SecurityManager.HashPassword(user.Password);

                // Insert user into database
                var sql = @"
                    INSERT INTO Users (Username, PasswordHash, Salt, Role, IsActive, CreatedBy)
                    VALUES (@Username, @PasswordHash, @Salt, @Role, @IsActive, @CreatedBy);
                    SELECT SCOPE_IDENTITY();";

                var parameters = new[]
                {
                    new SqlParameter("@Username", user.Username),
                    new SqlParameter("@PasswordHash", hashedPassword),
                    new SqlParameter("@Salt", salt),
                    new SqlParameter("@Role", user.Role),
                    new SqlParameter("@IsActive", user.IsActive),
                    new SqlParameter("@CreatedBy", createdBy)
                };

                var result = await DatabaseConnection.ExecuteScalarAsync(sql, parameters);
                var userId = Convert.ToInt32(result);

                // Log audit trail
                await LogAuditAsync("Users", userId, "INSERT", $"User created: {user.Username}", createdBy);

                return userId;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"فشل في إنشاء المستخدم: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Updates an existing user
        /// </summary>
        /// <param name="user">User object with updated details</param>
        /// <param name="modifiedBy">Username of modifier</param>
        /// <returns>True if update successful</returns>
        public async Task<bool> UpdateUserAsync(User user, string modifiedBy)
        {
            try
            {
                // Validate user data
                var validationErrors = user.Validate();
                if (validationErrors.Any())
                    throw new ArgumentException($"بيانات المستخدم غير صحيحة: {string.Join(", ", validationErrors)}");

                var sql = @"
                    UPDATE Users 
                    SET Role = @Role, IsActive = @IsActive, ModifiedDate = GETDATE(), ModifiedBy = @ModifiedBy
                    WHERE UserID = @UserID";

                var parameters = new[]
                {
                    new SqlParameter("@UserID", user.UserID),
                    new SqlParameter("@Role", user.Role),
                    new SqlParameter("@IsActive", user.IsActive),
                    new SqlParameter("@ModifiedBy", modifiedBy)
                };

                // Update password if provided
                if (!string.IsNullOrWhiteSpace(user.Password))
                {
                    var (hashedPassword, salt) = SecurityManager.HashPassword(user.Password);
                    sql = @"
                        UPDATE Users 
                        SET PasswordHash = @PasswordHash, Salt = @Salt, Role = @Role, IsActive = @IsActive, 
                            ModifiedDate = GETDATE(), ModifiedBy = @ModifiedBy
                        WHERE UserID = @UserID";

                    parameters = parameters.Concat(new[]
                    {
                        new SqlParameter("@PasswordHash", hashedPassword),
                        new SqlParameter("@Salt", salt)
                    }).ToArray();
                }

                var rowsAffected = await DatabaseConnection.ExecuteNonQueryAsync(sql, parameters);

                if (rowsAffected > 0)
                {
                    // Log audit trail
                    await LogAuditAsync("Users", user.UserID, "UPDATE", $"User updated: {user.Username}", modifiedBy);
                }

                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"فشل في تحديث المستخدم: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Deletes a user (soft delete)
        /// </summary>
        /// <param name="userId">User ID to delete</param>
        /// <param name="deletedBy">Username of deleter</param>
        /// <returns>True if deletion successful</returns>
        public async Task<bool> DeleteUserAsync(int userId, string deletedBy)
        {
            try
            {
                var sql = @"
                    UPDATE Users 
                    SET IsActive = 0, ModifiedDate = GETDATE(), ModifiedBy = @DeletedBy
                    WHERE UserID = @UserID";

                var parameters = new[]
                {
                    new SqlParameter("@UserID", userId),
                    new SqlParameter("@DeletedBy", deletedBy)
                };

                var rowsAffected = await DatabaseConnection.ExecuteNonQueryAsync(sql, parameters);

                if (rowsAffected > 0)
                {
                    // Log audit trail
                    await LogAuditAsync("Users", userId, "DELETE", "User deactivated", deletedBy);
                }

                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"فشل في حذف المستخدم: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Gets a user by username
        /// </summary>
        /// <param name="username">Username to search for</param>
        /// <returns>User object if found, null otherwise</returns>
        public async Task<User?> GetUserByUsernameAsync(string username)
        {
            try
            {
                var sql = @"
                    SELECT UserID, Username, PasswordHash, Salt, Role, IsActive, CreatedDate, 
                           LastLoginDate, FailedLoginAttempts, LockoutEndTime, CreatedBy, ModifiedDate, ModifiedBy
                    FROM Users 
                    WHERE Username = @Username";

                var parameters = new[] { new SqlParameter("@Username", username) };
                var dataTable = await DatabaseConnection.ExecuteQueryAsync(sql, parameters);

                if (dataTable.Rows.Count == 0)
                    return null;

                var row = dataTable.Rows[0];
                return MapDataRowToUser(row);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"فشل في البحث عن المستخدم: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Gets all users
        /// </summary>
        /// <param name="includeInactive">Include inactive users</param>
        /// <returns>List of users</returns>
        public async Task<List<User>> GetAllUsersAsync(bool includeInactive = false)
        {
            try
            {
                var sql = @"
                    SELECT UserID, Username, PasswordHash, Salt, Role, IsActive, CreatedDate, 
                           LastLoginDate, FailedLoginAttempts, LockoutEndTime, CreatedBy, ModifiedDate, ModifiedBy
                    FROM Users";

                if (!includeInactive)
                    sql += " WHERE IsActive = 1";

                sql += " ORDER BY Username";

                var dataTable = await DatabaseConnection.ExecuteQueryAsync(sql);
                var users = new List<User>();

                foreach (DataRow row in dataTable.Rows)
                {
                    users.Add(MapDataRowToUser(row));
                }

                return users;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"فشل في جلب المستخدمين: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Changes user password
        /// </summary>
        /// <param name="username">Username</param>
        /// <param name="currentPassword">Current password</param>
        /// <param name="newPassword">New password</param>
        /// <returns>True if password changed successfully</returns>
        public async Task<bool> ChangePasswordAsync(string username, string currentPassword, string newPassword)
        {
            try
            {
                // Authenticate with current password
                var user = await AuthenticateUserAsync(username, currentPassword);
                if (user == null)
                    throw new UnauthorizedAccessException("كلمة المرور الحالية غير صحيحة");

                // Validate new password
                var validation = SecurityManager.ValidatePasswordStrength(newPassword);
                if (!validation.IsValid)
                    throw new ArgumentException($"كلمة المرور الجديدة ضعيفة: {string.Join(", ", validation.Messages)}");

                // Hash new password
                var (hashedPassword, salt) = SecurityManager.HashPassword(newPassword);

                // Update password in database
                var sql = @"
                    UPDATE Users 
                    SET PasswordHash = @PasswordHash, Salt = @Salt, ModifiedDate = GETDATE(), ModifiedBy = @Username
                    WHERE Username = @Username";

                var parameters = new[]
                {
                    new SqlParameter("@PasswordHash", hashedPassword),
                    new SqlParameter("@Salt", salt),
                    new SqlParameter("@Username", username)
                };

                var rowsAffected = await DatabaseConnection.ExecuteNonQueryAsync(sql, parameters);

                if (rowsAffected > 0)
                {
                    // Log audit trail
                    await LogAuditAsync("Users", user.UserID, "UPDATE", "Password changed", username);
                }

                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"فشل في تغيير كلمة المرور: {ex.Message}", ex);
            }
        }

        #region Private Helper Methods

        /// <summary>
        /// Maps a DataRow to User object
        /// </summary>
        private static User MapDataRowToUser(DataRow row)
        {
            return new User
            {
                UserID = Convert.ToInt32(row["UserID"]),
                Username = row["Username"].ToString() ?? string.Empty,
                PasswordHash = row["PasswordHash"].ToString() ?? string.Empty,
                Salt = row["Salt"].ToString() ?? string.Empty,
                Role = row["Role"].ToString() ?? string.Empty,
                IsActive = Convert.ToBoolean(row["IsActive"]),
                CreatedDate = Convert.ToDateTime(row["CreatedDate"]),
                LastLoginDate = row["LastLoginDate"] == DBNull.Value ? null : Convert.ToDateTime(row["LastLoginDate"]),
                FailedLoginAttempts = Convert.ToInt32(row["FailedLoginAttempts"]),
                LockoutEndTime = row["LockoutEndTime"] == DBNull.Value ? null : Convert.ToDateTime(row["LockoutEndTime"]),
                CreatedBy = row["CreatedBy"].ToString(),
                ModifiedDate = row["ModifiedDate"] == DBNull.Value ? null : Convert.ToDateTime(row["ModifiedDate"]),
                ModifiedBy = row["ModifiedBy"].ToString()
            };
        }

        /// <summary>
        /// Increments failed login attempts for a user
        /// </summary>
        private async Task IncrementFailedLoginAttemptsAsync(string username)
        {
            var sql = @"
                UPDATE Users 
                SET FailedLoginAttempts = FailedLoginAttempts + 1,
                    LockoutEndTime = CASE 
                        WHEN FailedLoginAttempts >= 2 THEN DATEADD(MINUTE, 15, GETDATE())
                        ELSE LockoutEndTime 
                    END
                WHERE Username = @Username";

            var parameters = new[] { new SqlParameter("@Username", username) };
            await DatabaseConnection.ExecuteNonQueryAsync(sql, parameters);
        }

        /// <summary>
        /// Resets failed login attempts for a user
        /// </summary>
        private async Task ResetFailedLoginAttemptsAsync(string username)
        {
            var sql = @"
                UPDATE Users 
                SET FailedLoginAttempts = 0, LockoutEndTime = NULL
                WHERE Username = @Username";

            var parameters = new[] { new SqlParameter("@Username", username) };
            await DatabaseConnection.ExecuteNonQueryAsync(sql, parameters);
        }

        /// <summary>
        /// Updates last login date for a user
        /// </summary>
        private async Task UpdateLastLoginAsync(string username)
        {
            var sql = @"
                UPDATE Users 
                SET LastLoginDate = GETDATE()
                WHERE Username = @Username";

            var parameters = new[] { new SqlParameter("@Username", username) };
            await DatabaseConnection.ExecuteNonQueryAsync(sql, parameters);
        }

        /// <summary>
        /// Logs audit trail
        /// </summary>
        private async Task LogAuditAsync(string tableName, int recordId, string action, string details, string changedBy)
        {
            var sql = @"
                INSERT INTO AuditLog (TableName, RecordID, Action, NewValues, ChangedBy)
                VALUES (@TableName, @RecordID, @Action, @Details, @ChangedBy)";

            var parameters = new[]
            {
                new SqlParameter("@TableName", tableName),
                new SqlParameter("@RecordID", recordId),
                new SqlParameter("@Action", action),
                new SqlParameter("@Details", details),
                new SqlParameter("@ChangedBy", changedBy)
            };

            await DatabaseConnection.ExecuteNonQueryAsync(sql, parameters);
        }

        #endregion
    }
}
