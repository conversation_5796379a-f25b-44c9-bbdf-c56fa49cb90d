using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace MobileShopManagement.Services;

/// <summary>
/// خدمة الإعدادات المحدثة لـ .NET 9 - Modern configuration service for .NET 9
/// Provides strongly-typed configuration with hot reload support
/// </summary>
public sealed class ConfigurationService
{
    private static ConfigurationService? _instance;
    private static readonly object _lock = new();
    private readonly IConfiguration _configuration;
    private readonly ILogger<ConfigurationService> _logger;

    private ConfigurationService(IConfiguration configuration, ILogger<ConfigurationService> logger)
    {
        _configuration = configuration;
        _logger = logger;
    }

    /// <summary>
    /// Gets the singleton instance of ConfigurationService
    /// </summary>
    public static ConfigurationService Instance
    {
        get
        {
            if (_instance == null)
            {
                throw new InvalidOperationException("ConfigurationService has not been initialized. Call Initialize() first.");
            }
            return _instance;
        }
    }

    /// <summary>
    /// Initializes the configuration service
    /// </summary>
    public static void Initialize(IServiceProvider serviceProvider)
    {
        lock (_lock)
        {
            if (_instance == null)
            {
                var configuration = serviceProvider.GetRequiredService<IConfiguration>();
                var logger = serviceProvider.GetRequiredService<ILogger<ConfigurationService>>();
                _instance = new ConfigurationService(configuration, logger);
            }
        }
    }

    /// <summary>
    /// Application settings
    /// </summary>
    public ApplicationSettings Application => GetSection<ApplicationSettings>("ApplicationSettings");

    /// <summary>
    /// Security settings
    /// </summary>
    public SecuritySettings Security => GetSection<SecuritySettings>("SecuritySettings");

    /// <summary>
    /// UI settings
    /// </summary>
    public UISettings UI => GetSection<UISettings>("UISettings");

    /// <summary>
    /// Database settings
    /// </summary>
    public DatabaseSettings Database => GetSection<DatabaseSettings>("DatabaseSettings");

    /// <summary>
    /// Performance settings
    /// </summary>
    public PerformanceSettings Performance => GetSection<PerformanceSettings>("PerformanceSettings");

    /// <summary>
    /// Feature flags
    /// </summary>
    public FeatureFlags Features => GetSection<FeatureFlags>("FeatureFlags");

    /// <summary>
    /// Gets connection string
    /// </summary>
    public string GetConnectionString(string name = "MobileShopDB")
    {
        var connectionString = _configuration.GetConnectionString(name);
        if (string.IsNullOrEmpty(connectionString))
        {
            _logger.LogError("Connection string '{ConnectionStringName}' not found", name);
            throw new InvalidOperationException($"Connection string '{name}' not found in configuration.");
        }
        return connectionString;
    }

    /// <summary>
    /// Gets a configuration section as strongly-typed object
    /// </summary>
    private T GetSection<T>(string sectionName) where T : new()
    {
        try
        {
            var section = _configuration.GetSection(sectionName);
            var result = section.Get<T>() ?? new T();
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get configuration section '{SectionName}'", sectionName);
            return new T();
        }
    }

    /// <summary>
    /// Gets a configuration value
    /// </summary>
    public T GetValue<T>(string key, T defaultValue = default!)
    {
        try
        {
            return _configuration.GetValue<T>(key, defaultValue);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get configuration value '{Key}'", key);
            return defaultValue;
        }
    }

    /// <summary>
    /// Checks if a feature is enabled
    /// </summary>
    public bool IsFeatureEnabled(string featureName)
    {
        return GetValue<bool>($"FeatureFlags:{featureName}", false);
    }
}

/// <summary>
/// Application settings configuration
/// </summary>
public record ApplicationSettings
{
    public string ApplicationTitle { get; init; } = "نظام إدارة متجر الهواتف المحمولة";
    public string CompanyName { get; init; } = "متجر الهواتف المحمولة";
    public string DefaultLanguage { get; init; } = "ar";
    public int SessionTimeoutMinutes { get; init; } = 30;
    public string BackupDirectory { get; init; } = @"C:\MobileShopBackups";
    public string ReportsDirectory { get; init; } = @"C:\MobileShopReports";
    public string ExportsDirectory { get; init; } = @"C:\MobileShopExports";
    public string LogsDirectory { get; init; } = @"C:\MobileShopLogs";
    public string TemplatesDirectory { get; init; } = "Templates";
}

/// <summary>
/// Security settings configuration
/// </summary>
public record SecuritySettings
{
    public int PasswordMinLength { get; init; } = 6;
    public int MaxLoginAttempts { get; init; } = 3;
    public int LockoutDurationMinutes { get; init; } = 15;
    public int SessionTokenExpiryMinutes { get; init; } = 60;
    public bool EnableTwoFactorAuth { get; init; } = false;
    public bool RequirePasswordComplexity { get; init; } = true;
    public int PasswordHistoryCount { get; init; } = 5;
    public bool AutoLockoutEnabled { get; init; } = true;
}

/// <summary>
/// UI settings configuration
/// </summary>
public record UISettings
{
    public string DefaultFont { get; init; } = "Tahoma";
    public float DefaultFontSize { get; init; } = 9f;
    public int GridRowHeight { get; init; } = 25;
    public string Theme { get; init; } = "Default";
    public bool EnableAnimations { get; init; } = true;
    public bool ShowTooltips { get; init; } = true;
    public int AutoSaveInterval { get; init; } = 300;
    public FormSize DefaultFormSize { get; init; } = new();
}

/// <summary>
/// Form size configuration
/// </summary>
public record FormSize
{
    public int Width { get; init; } = 1024;
    public int Height { get; init; } = 768;
}

/// <summary>
/// Database settings configuration
/// </summary>
public record DatabaseSettings
{
    public int CommandTimeout { get; init; } = 30;
    public int ConnectionTimeout { get; init; } = 30;
    public bool EnableRetry { get; init; } = true;
    public int MaxRetryAttempts { get; init; } = 3;
    public int RetryDelay { get; init; } = 1000;
    public bool EnableConnectionPooling { get; init; } = true;
    public int BackupRetentionDays { get; init; } = 30;
    public bool AutoBackupEnabled { get; init; } = false;
    public TimeSpan AutoBackupTime { get; init; } = new(2, 0, 0);
}

/// <summary>
/// Performance settings configuration
/// </summary>
public record PerformanceSettings
{
    public bool EnableCaching { get; init; } = true;
    public int CacheExpiryMinutes { get; init; } = 30;
    public int MaxCacheSize { get; init; } = 100;
    public bool EnableLazyLoading { get; init; } = true;
    public int BatchSize { get; init; } = 1000;
    public bool EnableAsyncOperations { get; init; } = true;
}

/// <summary>
/// Feature flags configuration
/// </summary>
public record FeatureFlags
{
    public bool EnableAdvancedReports { get; init; } = true;
    public bool EnableDataExport { get; init; } = true;
    public bool EnableUserManagement { get; init; } = true;
    public bool EnableBackupRestore { get; init; } = true;
    public bool EnableAuditLogging { get; init; } = true;
    public bool EnableNotifications { get; init; } = true;
    public bool EnableDarkMode { get; init; } = false;
    public bool EnableCloudSync { get; init; } = false;
}

/// <summary>
/// Configuration builder extensions for .NET 9
/// </summary>
public static class ConfigurationExtensions
{
    /// <summary>
    /// Builds configuration for the mobile shop application
    /// </summary>
    public static IConfiguration BuildConfiguration()
    {
        var builder = new ConfigurationBuilder()
            .SetBasePath(AppDomain.CurrentDomain.BaseDirectory)
            .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
            .AddJsonFile($"appsettings.{Environment.GetEnvironmentVariable("DOTNET_ENVIRONMENT") ?? "Production"}.json", optional: true, reloadOnChange: true)
            .AddEnvironmentVariables("MOBILESHOP_")
            .AddUserSecrets<ConfigurationService>(optional: true);

        return builder.Build();
    }

    /// <summary>
    /// Validates configuration on startup
    /// </summary>
    public static void ValidateConfiguration(this IConfiguration configuration)
    {
        var requiredSettings = new[]
        {
            "ConnectionStrings:MobileShopDB",
            "ApplicationSettings:ApplicationTitle",
            "SecuritySettings:PasswordMinLength"
        };

        var missingSettings = new List<string>();

        foreach (var setting in requiredSettings)
        {
            if (string.IsNullOrEmpty(configuration[setting]))
            {
                missingSettings.Add(setting);
            }
        }

        if (missingSettings.Count != 0)
        {
            throw new InvalidOperationException($"Missing required configuration settings: {string.Join(", ", missingSettings)}");
        }
    }

    /// <summary>
    /// Creates required directories from configuration
    /// </summary>
    public static void EnsureDirectoriesExist(this IConfiguration configuration)
    {
        var appSettings = configuration.GetSection("ApplicationSettings").Get<ApplicationSettings>() ?? new ApplicationSettings();
        
        var directories = new[]
        {
            appSettings.BackupDirectory,
            appSettings.ReportsDirectory,
            appSettings.ExportsDirectory,
            appSettings.LogsDirectory,
            Path.Combine(AppDomain.CurrentDomain.BaseDirectory, appSettings.TemplatesDirectory)
        };

        foreach (var directory in directories)
        {
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }
        }
    }
}
