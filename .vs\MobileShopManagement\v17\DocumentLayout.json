{"Version": 1, "WorkspaceRootPath": "F:\\MySystem\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|MobileShopManagement\\MobileShopManagement.csproj|f:\\mysystem\\mobileshopmanagement\\dataaccess\\databaseconnection.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|MobileShopManagement\\MobileShopManagement.csproj|solutionrelative:mobileshopmanagement\\dataaccess\\databaseconnection.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|MobileShopManagement\\MobileShopManagement.csproj|f:\\mysystem\\mobileshopmanagement\\models\\sale.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|MobileShopManagement\\MobileShopManagement.csproj|solutionrelative:mobileshopmanagement\\models\\sale.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|MobileShopManagement\\MobileShopManagement.csproj|f:\\mysystem\\mobileshopmanagement\\models\\user.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|MobileShopManagement\\MobileShopManagement.csproj|solutionrelative:mobileshopmanagement\\models\\user.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 2, "Children": [{"$type": "Document", "DocumentIndex": 2, "Title": "User.cs", "DocumentMoniker": "F:\\MySystem\\MobileShopManagement\\Models\\User.cs", "RelativeDocumentMoniker": "MobileShopManagement\\Models\\User.cs", "ToolTip": "F:\\MySystem\\MobileShopManagement\\Models\\User.cs", "RelativeToolTip": "MobileShopManagement\\Models\\User.cs", "ViewState": "AgIAAL0AAAAAAAAAAAAgwM8AAAAwAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-15T11:00:29.431Z", "IsPinned": true, "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "Sale.cs", "DocumentMoniker": "F:\\MySystem\\MobileShopManagement\\Models\\Sale.cs", "RelativeDocumentMoniker": "MobileShopManagement\\Models\\Sale.cs", "ToolTip": "F:\\MySystem\\MobileShopManagement\\Models\\Sale.cs", "RelativeToolTip": "MobileShopManagement\\Models\\Sale.cs", "ViewState": "AgIAAPkAAAAAAAAAAAAgwA8BAAAOAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-15T11:01:39.035Z", "IsPinned": true, "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "DatabaseConnection.cs", "DocumentMoniker": "F:\\MySystem\\MobileShopManagement\\DataAccess\\DatabaseConnection.cs", "RelativeDocumentMoniker": "MobileShopManagement\\DataAccess\\DatabaseConnection.cs", "ToolTip": "F:\\MySystem\\MobileShopManagement\\DataAccess\\DatabaseConnection.cs", "RelativeToolTip": "MobileShopManagement\\DataAccess\\DatabaseConnection.cs", "ViewState": "AgIAANkAAAAAAAAAAAAkwPUAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-15T11:03:14.255Z", "IsPinned": true, "EditorCaption": ""}]}]}]}