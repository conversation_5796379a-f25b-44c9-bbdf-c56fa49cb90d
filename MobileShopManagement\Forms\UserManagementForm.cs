using MobileShopManagement.BusinessLogic;
using MobileShopManagement.Models;
using MobileShopManagement.Properties;
using System.Configuration;

namespace MobileShopManagement.Forms
{
    /// <summary>
    /// نموذج إدارة المستخدمين - User management form
    /// Form for managing system users (Admin only)
    /// </summary>
    public partial class UserManagementForm : Form
    {
        private readonly UserManager _userManager;
        private List<User> _currentUsers;

        /// <summary>
        /// Constructor
        /// </summary>
        public UserManagementForm()
        {
            InitializeComponent();
            _userManager = new UserManager();
            _currentUsers = new List<User>();
            SetupForm();
        }

        /// <summary>
        /// Sets up the form with Arabic RTL support and styling
        /// </summary>
        private void SetupForm()
        {
            // Check admin permission
            if (!SessionManager.IsAdmin)
            {
                MessageBox.Show("ليس لديك صلاحية لإدارة المستخدمين", "غير مصرح",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
                this.Close();
                return;
            }

            // Set RTL support
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // Set form properties
            this.Text = "إدارة المستخدمين";
            this.Size = new Size(900, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.BackColor = Color.FromArgb(250, 250, 250);

            // Set font
            var fontName = ConfigurationManager.AppSettings["DefaultFont"] ?? "Tahoma";
            var fontSize = float.TryParse(ConfigurationManager.AppSettings["DefaultFontSize"], out var size) ? size : 9f;
            this.Font = new Font(fontName, fontSize);

            CreateControls();
            SetupEventHandlers();
            LoadUsersData();
        }

        /// <summary>
        /// Creates and positions form controls
        /// </summary>
        private void CreateControls()
        {
            // Title panel
            var titlePanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 60,
                BackColor = Color.FromArgb(63, 81, 181),
                Padding = new Padding(20)
            };

            var lblTitle = new Label
            {
                Text = "إدارة المستخدمين",
                Font = new Font(this.Font.FontFamily, 16, FontStyle.Bold),
                ForeColor = Color.White,
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter
            };
            titlePanel.Controls.Add(lblTitle);
            this.Controls.Add(titlePanel);

            // Main content panel
            var contentPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20)
            };

            // Users data grid
            CreateUsersDataGrid(contentPanel);

            // User details panel
            CreateUserDetailsPanel(contentPanel);

            this.Controls.Add(contentPanel);

            // Buttons panel
            CreateButtonsPanel();
        }

        /// <summary>
        /// Creates the users data grid
        /// </summary>
        private void CreateUsersDataGrid(Panel parent)
        {
            var gridPanel = new Panel
            {
                Location = new Point(20, 20),
                Size = new Size(500, 400),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            var lblGridTitle = new Label
            {
                Text = "قائمة المستخدمين",
                Font = new Font(this.Font.FontFamily, 12, FontStyle.Bold),
                Location = new Point(10, 10),
                Size = new Size(480, 25),
                TextAlign = ContentAlignment.MiddleCenter
            };
            gridPanel.Controls.Add(lblGridTitle);

            dgvUsers = new DataGridView
            {
                Location = new Point(10, 45),
                Size = new Size(480, 340),
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal,
                ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.None,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                ReadOnly = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                AllowUserToResizeRows = false,
                RowHeadersVisible = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                Font = new Font(this.Font.FontFamily, 9)
            };

            // Style headers
            dgvUsers.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(63, 81, 181);
            dgvUsers.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvUsers.ColumnHeadersDefaultCellStyle.Font = new Font(this.Font.FontFamily, 10, FontStyle.Bold);
            dgvUsers.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgvUsers.ColumnHeadersHeight = 35;

            // Style rows
            dgvUsers.DefaultCellStyle.BackColor = Color.White;
            dgvUsers.DefaultCellStyle.ForeColor = Color.Black;
            dgvUsers.DefaultCellStyle.SelectionBackColor = Color.FromArgb(200, 230, 255);
            dgvUsers.DefaultCellStyle.SelectionForeColor = Color.Black;
            dgvUsers.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgvUsers.RowTemplate.Height = 30;

            SetupUsersDataGridColumns();
            gridPanel.Controls.Add(dgvUsers);
            parent.Controls.Add(gridPanel);
        }

        /// <summary>
        /// Sets up users data grid columns
        /// </summary>
        private void SetupUsersDataGridColumns()
        {
            dgvUsers.Columns.Clear();

            dgvUsers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Username",
                HeaderText = "اسم المستخدم",
                DataPropertyName = "Username",
                Width = 120,
                DefaultCellStyle = { Alignment = DataGridViewContentAlignment.MiddleCenter }
            });

            dgvUsers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Role",
                HeaderText = "الدور",
                DataPropertyName = "RoleInArabic",
                Width = 80,
                DefaultCellStyle = { Alignment = DataGridViewContentAlignment.MiddleCenter }
            });

            dgvUsers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Status",
                HeaderText = "الحالة",
                DataPropertyName = "StatusInArabic",
                Width = 80,
                DefaultCellStyle = { Alignment = DataGridViewContentAlignment.MiddleCenter }
            });

            dgvUsers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "LastLogin",
                HeaderText = "آخر دخول",
                DataPropertyName = "LastLoginDate",
                Width = 120,
                DefaultCellStyle = { 
                    Alignment = DataGridViewContentAlignment.MiddleCenter,
                    Format = "yyyy/MM/dd HH:mm"
                }
            });
        }

        /// <summary>
        /// Creates the user details panel
        /// </summary>
        private void CreateUserDetailsPanel(Panel parent)
        {
            var detailsPanel = new Panel
            {
                Location = new Point(540, 20),
                Size = new Size(300, 400),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            var lblDetailsTitle = new Label
            {
                Text = "تفاصيل المستخدم",
                Font = new Font(this.Font.FontFamily, 12, FontStyle.Bold),
                Location = new Point(10, 10),
                Size = new Size(280, 25),
                TextAlign = ContentAlignment.MiddleCenter
            };
            detailsPanel.Controls.Add(lblDetailsTitle);

            var yPos = 50;
            var labelWidth = 100;
            var controlWidth = 180;
            var spacing = 35;

            // Username
            var lblUsername = new Label
            {
                Text = "اسم المستخدم *:",
                Location = new Point(190, yPos),
                Size = new Size(labelWidth, 23),
                TextAlign = ContentAlignment.MiddleRight
            };
            detailsPanel.Controls.Add(lblUsername);

            txtUsername = new TextBox
            {
                Location = new Point(10, yPos),
                Size = new Size(controlWidth, 23),
                TextAlign = HorizontalAlignment.Right
            };
            detailsPanel.Controls.Add(txtUsername);
            yPos += spacing;

            // Password
            var lblPassword = new Label
            {
                Text = "كلمة المرور *:",
                Location = new Point(190, yPos),
                Size = new Size(labelWidth, 23),
                TextAlign = ContentAlignment.MiddleRight
            };
            detailsPanel.Controls.Add(lblPassword);

            txtPassword = new TextBox
            {
                Location = new Point(10, yPos),
                Size = new Size(controlWidth, 23),
                UseSystemPasswordChar = true,
                TextAlign = HorizontalAlignment.Right
            };
            detailsPanel.Controls.Add(txtPassword);
            yPos += spacing;

            // Confirm Password
            var lblConfirmPassword = new Label
            {
                Text = "تأكيد كلمة المرور *:",
                Location = new Point(190, yPos),
                Size = new Size(labelWidth, 23),
                TextAlign = ContentAlignment.MiddleRight
            };
            detailsPanel.Controls.Add(lblConfirmPassword);

            txtConfirmPassword = new TextBox
            {
                Location = new Point(10, yPos),
                Size = new Size(controlWidth, 23),
                UseSystemPasswordChar = true,
                TextAlign = HorizontalAlignment.Right
            };
            detailsPanel.Controls.Add(txtConfirmPassword);
            yPos += spacing;

            // Role
            var lblRole = new Label
            {
                Text = "الدور *:",
                Location = new Point(190, yPos),
                Size = new Size(labelWidth, 23),
                TextAlign = ContentAlignment.MiddleRight
            };
            detailsPanel.Controls.Add(lblRole);

            cmbRole = new ComboBox
            {
                Location = new Point(10, yPos),
                Size = new Size(controlWidth, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbRole.Items.AddRange(new[] { "Admin", "Cashier" });
            cmbRole.SelectedIndex = 1; // Default to Cashier
            detailsPanel.Controls.Add(cmbRole);
            yPos += spacing;

            // Active checkbox
            chkIsActive = new CheckBox
            {
                Text = "المستخدم نشط",
                Location = new Point(10, yPos),
                Size = new Size(controlWidth, 23),
                Checked = true,
                TextAlign = ContentAlignment.MiddleRight
            };
            detailsPanel.Controls.Add(chkIsActive);
            yPos += 50;

            // Action buttons
            btnAddUser = new Button
            {
                Text = "إضافة مستخدم",
                Location = new Point(10, yPos),
                Size = new Size(120, 30),
                BackColor = Color.FromArgb(76, 175, 80),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font(this.Font.FontFamily, 9, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            btnAddUser.FlatAppearance.BorderSize = 0;
            detailsPanel.Controls.Add(btnAddUser);

            btnUpdateUser = new Button
            {
                Text = "تحديث",
                Location = new Point(140, yPos),
                Size = new Size(70, 30),
                BackColor = Color.FromArgb(255, 152, 0),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font(this.Font.FontFamily, 9, FontStyle.Bold),
                Cursor = Cursors.Hand,
                Enabled = false
            };
            btnUpdateUser.FlatAppearance.BorderSize = 0;
            detailsPanel.Controls.Add(btnUpdateUser);

            btnDeleteUser = new Button
            {
                Text = "حذف",
                Location = new Point(220, yPos),
                Size = new Size(70, 30),
                BackColor = Color.FromArgb(244, 67, 54),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font(this.Font.FontFamily, 9, FontStyle.Bold),
                Cursor = Cursors.Hand,
                Enabled = false
            };
            btnDeleteUser.FlatAppearance.BorderSize = 0;
            detailsPanel.Controls.Add(btnDeleteUser);
            yPos += 40;

            btnClearForm = new Button
            {
                Text = "مسح النموذج",
                Location = new Point(10, yPos),
                Size = new Size(120, 30),
                BackColor = Color.FromArgb(158, 158, 158),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font(this.Font.FontFamily, 9, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            btnClearForm.FlatAppearance.BorderSize = 0;
            detailsPanel.Controls.Add(btnClearForm);

            parent.Controls.Add(detailsPanel);
        }

        /// <summary>
        /// Creates the buttons panel
        /// </summary>
        private void CreateButtonsPanel()
        {
            var buttonsPanel = new Panel
            {
                Dock = DockStyle.Bottom,
                Height = 60,
                BackColor = Color.FromArgb(240, 240, 240),
                Padding = new Padding(20, 15, 20, 15)
            };

            btnRefresh = new Button
            {
                Text = "تحديث القائمة",
                Location = new Point(20, 15),
                Size = new Size(120, 30),
                BackColor = Color.FromArgb(33, 150, 243),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font(this.Font.FontFamily, 9, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            btnRefresh.FlatAppearance.BorderSize = 0;
            buttonsPanel.Controls.Add(btnRefresh);

            btnClose = new Button
            {
                Text = Resources.Close,
                Location = new Point(750, 15),
                Size = new Size(100, 30),
                BackColor = Color.FromArgb(158, 158, 158),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font(this.Font.FontFamily, 9, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            btnClose.FlatAppearance.BorderSize = 0;
            buttonsPanel.Controls.Add(btnClose);

            this.Controls.Add(buttonsPanel);
        }

        /// <summary>
        /// Sets up event handlers
        /// </summary>
        private void SetupEventHandlers()
        {
            dgvUsers.SelectionChanged += DgvUsers_SelectionChanged;
            btnAddUser.Click += BtnAddUser_Click;
            btnUpdateUser.Click += BtnUpdateUser_Click;
            btnDeleteUser.Click += BtnDeleteUser_Click;
            btnClearForm.Click += BtnClearForm_Click;
            btnRefresh.Click += BtnRefresh_Click;
            btnClose.Click += BtnClose_Click;
        }

        /// <summary>
        /// Loads users data
        /// </summary>
        private async void LoadUsersData()
        {
            try
            {
                _currentUsers = await _userManager.GetAllUsersAsync(true);
                dgvUsers.DataSource = _currentUsers;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المستخدمين: {ex.Message}", Resources.Error,
                    MessageBoxButtons.OK, MessageBoxIcon.Error,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
            }
        }

        /// <summary>
        /// Clears the form
        /// </summary>
        private void ClearForm()
        {
            txtUsername.Clear();
            txtPassword.Clear();
            txtConfirmPassword.Clear();
            cmbRole.SelectedIndex = 1;
            chkIsActive.Checked = true;
            btnUpdateUser.Enabled = false;
            btnDeleteUser.Enabled = false;
            btnAddUser.Enabled = true;
        }

        /// <summary>
        /// Loads user data into form
        /// </summary>
        private void LoadUserIntoForm(User user)
        {
            txtUsername.Text = user.Username;
            txtPassword.Clear();
            txtConfirmPassword.Clear();
            cmbRole.SelectedItem = user.Role;
            chkIsActive.Checked = user.IsActive;
            btnUpdateUser.Enabled = true;
            btnDeleteUser.Enabled = user.Username != SessionManager.CurrentUsername; // Can't delete self
            btnAddUser.Enabled = false;
        }

        #region Event Handlers

        private void DgvUsers_SelectionChanged(object? sender, EventArgs e)
        {
            if (dgvUsers.SelectedRows.Count > 0)
            {
                var selectedUser = (User)dgvUsers.SelectedRows[0].DataBoundItem;
                LoadUserIntoForm(selectedUser);
            }
        }

        private async void BtnAddUser_Click(object? sender, EventArgs e)
        {
            try
            {
                var user = new User
                {
                    Username = txtUsername.Text.Trim(),
                    Password = txtPassword.Text,
                    ConfirmPassword = txtConfirmPassword.Text,
                    Role = cmbRole.SelectedItem?.ToString() ?? "Cashier",
                    IsActive = chkIsActive.Checked
                };

                var userId = await _userManager.CreateUserAsync(user, SessionManager.CurrentUsername);
                
                MessageBox.Show("تم إضافة المستخدم بنجاح", Resources.Success,
                    MessageBoxButtons.OK, MessageBoxIcon.Information,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);

                ClearForm();
                LoadUsersData();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة المستخدم: {ex.Message}", Resources.Error,
                    MessageBoxButtons.OK, MessageBoxIcon.Error,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
            }
        }

        private async void BtnUpdateUser_Click(object? sender, EventArgs e)
        {
            try
            {
                if (dgvUsers.SelectedRows.Count == 0) return;

                var selectedUser = (User)dgvUsers.SelectedRows[0].DataBoundItem;
                selectedUser.Role = cmbRole.SelectedItem?.ToString() ?? "Cashier";
                selectedUser.IsActive = chkIsActive.Checked;
                
                if (!string.IsNullOrWhiteSpace(txtPassword.Text))
                {
                    selectedUser.Password = txtPassword.Text;
                    selectedUser.ConfirmPassword = txtConfirmPassword.Text;
                }

                var success = await _userManager.UpdateUserAsync(selectedUser, SessionManager.CurrentUsername);
                
                if (success)
                {
                    MessageBox.Show("تم تحديث المستخدم بنجاح", Resources.Success,
                        MessageBoxButtons.OK, MessageBoxIcon.Information,
                        MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);

                    ClearForm();
                    LoadUsersData();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث المستخدم: {ex.Message}", Resources.Error,
                    MessageBoxButtons.OK, MessageBoxIcon.Error,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
            }
        }

        private async void BtnDeleteUser_Click(object? sender, EventArgs e)
        {
            try
            {
                if (dgvUsers.SelectedRows.Count == 0) return;

                var selectedUser = (User)dgvUsers.SelectedRows[0].DataBoundItem;
                
                if (selectedUser.Username == SessionManager.CurrentUsername)
                {
                    MessageBox.Show("لا يمكنك حذف حسابك الخاص", "تحذير",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning,
                        MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
                    return;
                }

                var result = MessageBox.Show($"هل تريد حذف المستخدم: {selectedUser.Username}؟",
                    "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Warning,
                    MessageBoxDefaultButton.Button2, MessageBoxOptions.RtlReading);

                if (result == DialogResult.Yes)
                {
                    var success = await _userManager.DeleteUserAsync(selectedUser.UserID, SessionManager.CurrentUsername);
                    
                    if (success)
                    {
                        MessageBox.Show("تم حذف المستخدم بنجاح", Resources.Success,
                            MessageBoxButtons.OK, MessageBoxIcon.Information,
                            MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);

                        ClearForm();
                        LoadUsersData();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف المستخدم: {ex.Message}", Resources.Error,
                    MessageBoxButtons.OK, MessageBoxIcon.Error,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
            }
        }

        private void BtnClearForm_Click(object? sender, EventArgs e)
        {
            ClearForm();
        }

        private void BtnRefresh_Click(object? sender, EventArgs e)
        {
            LoadUsersData();
        }

        private void BtnClose_Click(object? sender, EventArgs e)
        {
            this.Close();
        }

        #endregion

        #region Form Controls
        private DataGridView dgvUsers = null!;
        private TextBox txtUsername = null!;
        private TextBox txtPassword = null!;
        private TextBox txtConfirmPassword = null!;
        private ComboBox cmbRole = null!;
        private CheckBox chkIsActive = null!;
        private Button btnAddUser = null!;
        private Button btnUpdateUser = null!;
        private Button btnDeleteUser = null!;
        private Button btnClearForm = null!;
        private Button btnRefresh = null!;
        private Button btnClose = null!;
        #endregion
    }
}
