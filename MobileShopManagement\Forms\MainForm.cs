using MobileShopManagement.BusinessLogic;
using MobileShopManagement.Properties;
using System.Configuration;

namespace MobileShopManagement.Forms
{
    /// <summary>
    /// النموذج الرئيسي - Main form
    /// Central dashboard for the mobile shop management system
    /// </summary>
    public partial class MainForm : Form
    {
        private readonly SalesManager _salesManager;
        private readonly Timer _sessionTimer;
        private readonly Timer _clockTimer;

        /// <summary>
        /// Constructor
        /// </summary>
        public MainForm()
        {
            InitializeComponent();
            _salesManager = new SalesManager();
            
            // Initialize timers
            _sessionTimer = new Timer { Interval = 60000 }; // Check every minute
            _sessionTimer.Tick += SessionTimer_Tick;
            _sessionTimer.Start();

            _clockTimer = new Timer { Interval = 1000 }; // Update every second
            _clockTimer.Tick += ClockTimer_Tick;
            _clockTimer.Start();

            SetupForm();
            SetupEventHandlers();
        }

        /// <summary>
        /// Sets up the form with Arabic RTL support and styling
        /// </summary>
        private void SetupForm()
        {
            // Set RTL support
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // Set form properties
            this.Text = Resources.MainTitle;
            this.WindowState = FormWindowState.Maximized;
            this.BackColor = Color.FromArgb(245, 245, 245);
            this.Icon = SystemIcons.Application;

            // Set font
            var fontName = ConfigurationManager.AppSettings["DefaultFont"] ?? "Tahoma";
            var fontSize = float.TryParse(ConfigurationManager.AppSettings["DefaultFontSize"], out var size) ? size : 9f;
            this.Font = new Font(fontName, fontSize);

            // Create menu bar
            CreateMenuBar();
            
            // Create toolbar
            CreateToolBar();
            
            // Create status bar
            CreateStatusBar();
            
            // Create main content area
            CreateMainContent();

            // Update user info
            UpdateUserInfo();
        }

        /// <summary>
        /// Creates the main menu bar
        /// </summary>
        private void CreateMenuBar()
        {
            var menuStrip = new MenuStrip
            {
                RightToLeft = RightToLeft.Yes,
                Font = this.Font,
                BackColor = Color.FromArgb(63, 81, 181),
                ForeColor = Color.White
            };

            // File menu
            var fileMenu = new ToolStripMenuItem(Resources.File);
            fileMenu.DropDownItems.Add(new ToolStripMenuItem("تسجيل خروج", null, Logout_Click));
            fileMenu.DropDownItems.Add(new ToolStripSeparator());
            fileMenu.DropDownItems.Add(new ToolStripMenuItem("خروج", null, Exit_Click));

            // Sales menu
            var salesMenu = new ToolStripMenuItem(Resources.Sales);
            salesMenu.DropDownItems.Add(new ToolStripMenuItem(Resources.AddSale, null, AddSale_Click));
            salesMenu.DropDownItems.Add(new ToolStripMenuItem("عرض المبيعات", null, ViewSales_Click));
            salesMenu.DropDownItems.Add(new ToolStripSeparator());
            salesMenu.DropDownItems.Add(new ToolStripMenuItem(Resources.Export, null, ExportSales_Click));

            // Reports menu
            var reportsMenu = new ToolStripMenuItem(Resources.Reports);
            reportsMenu.DropDownItems.Add(new ToolStripMenuItem("تقرير يومي", null, DailyReport_Click));
            reportsMenu.DropDownItems.Add(new ToolStripMenuItem("تقرير شهري", null, MonthlyReport_Click));
            reportsMenu.DropDownItems.Add(new ToolStripMenuItem("تقرير المنتجات", null, ProductReport_Click));

            // Users menu (Admin only)
            var usersMenu = new ToolStripMenuItem(Resources.Users);
            usersMenu.DropDownItems.Add(new ToolStripMenuItem("إدارة المستخدمين", null, ManageUsers_Click));
            usersMenu.DropDownItems.Add(new ToolStripMenuItem("تغيير كلمة المرور", null, ChangePassword_Click));
            usersMenu.Visible = SessionManager.IsAdmin;

            // Settings menu (Admin only)
            var settingsMenu = new ToolStripMenuItem(Resources.Settings);
            settingsMenu.DropDownItems.Add(new ToolStripMenuItem(Resources.Backup, null, CreateBackup_Click));
            settingsMenu.DropDownItems.Add(new ToolStripMenuItem("إعدادات النظام", null, SystemSettings_Click));
            settingsMenu.Visible = SessionManager.IsAdmin;

            // Help menu
            var helpMenu = new ToolStripMenuItem(Resources.Help);
            helpMenu.DropDownItems.Add(new ToolStripMenuItem("حول البرنامج", null, About_Click));
            helpMenu.DropDownItems.Add(new ToolStripMenuItem("دليل المستخدم", null, UserGuide_Click));

            // Add menus to menu strip
            menuStrip.Items.AddRange(new ToolStripItem[] 
            { 
                fileMenu, salesMenu, reportsMenu, usersMenu, settingsMenu, helpMenu 
            });

            this.MainMenuStrip = menuStrip;
            this.Controls.Add(menuStrip);
        }

        /// <summary>
        /// Creates the toolbar
        /// </summary>
        private void CreateToolBar()
        {
            var toolStrip = new ToolStrip
            {
                RightToLeft = RightToLeft.Yes,
                Font = this.Font,
                BackColor = Color.FromArgb(250, 250, 250),
                GripStyle = ToolStripGripStyle.Hidden,
                ImageScalingSize = new Size(24, 24)
            };

            // Add Sale button
            var addSaleBtn = new ToolStripButton
            {
                Text = Resources.AddSale,
                DisplayStyle = ToolStripItemDisplayStyle.ImageAndText,
                TextImageRelation = TextImageRelation.ImageBeforeText
            };
            addSaleBtn.Click += AddSale_Click;

            // Export button
            var exportBtn = new ToolStripButton
            {
                Text = Resources.Export,
                DisplayStyle = ToolStripItemDisplayStyle.ImageAndText,
                TextImageRelation = TextImageRelation.ImageBeforeText
            };
            exportBtn.Click += ExportSales_Click;

            // Print button
            var printBtn = new ToolStripButton
            {
                Text = Resources.Print,
                DisplayStyle = ToolStripItemDisplayStyle.ImageAndText,
                TextImageRelation = TextImageRelation.ImageBeforeText
            };
            printBtn.Click += PrintReport_Click;

            // Backup button (Admin only)
            var backupBtn = new ToolStripButton
            {
                Text = Resources.Backup,
                DisplayStyle = ToolStripItemDisplayStyle.ImageAndText,
                TextImageRelation = TextImageRelation.ImageBeforeText,
                Visible = SessionManager.IsAdmin
            };
            backupBtn.Click += CreateBackup_Click;

            // Add buttons to toolbar
            toolStrip.Items.AddRange(new ToolStripItem[] 
            { 
                addSaleBtn, 
                new ToolStripSeparator(),
                exportBtn, 
                printBtn,
                new ToolStripSeparator(),
                backupBtn
            });

            this.Controls.Add(toolStrip);
        }

        /// <summary>
        /// Creates the status bar
        /// </summary>
        private void CreateStatusBar()
        {
            statusStrip = new StatusStrip
            {
                RightToLeft = RightToLeft.Yes,
                Font = this.Font,
                BackColor = Color.FromArgb(240, 240, 240)
            };

            // User info label
            lblUserInfo = new ToolStripStatusLabel
            {
                Text = $"المستخدم: {SessionManager.CurrentUsername} ({SessionManager.CurrentUserRoleArabic})",
                Spring = true,
                TextAlign = ContentAlignment.MiddleRight
            };

            // Clock label
            lblClock = new ToolStripStatusLabel
            {
                Text = DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss"),
                TextAlign = ContentAlignment.MiddleLeft
            };

            // Session info label
            lblSessionInfo = new ToolStripStatusLabel
            {
                Text = "الجلسة نشطة",
                TextAlign = ContentAlignment.MiddleCenter
            };

            statusStrip.Items.AddRange(new ToolStripItem[] { lblUserInfo, lblSessionInfo, lblClock });
            this.Controls.Add(statusStrip);
        }

        /// <summary>
        /// Creates the main content area
        /// </summary>
        private void CreateMainContent()
        {
            // Create main panel
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Padding = new Padding(10)
            };

            // Create welcome panel
            var welcomePanel = new Panel
            {
                Size = new Size(400, 200),
                BackColor = Color.FromArgb(33, 150, 243),
                Location = new Point(50, 50)
            };

            var welcomeLabel = new Label
            {
                Text = $"مرحباً {SessionManager.CurrentUsername}\nفي نظام إدارة متجر الهواتف المحمولة",
                Font = new Font(this.Font.FontFamily, 16, FontStyle.Bold),
                ForeColor = Color.White,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };

            welcomePanel.Controls.Add(welcomeLabel);
            mainPanel.Controls.Add(welcomePanel);

            // Create quick stats panel
            CreateQuickStatsPanel(mainPanel);

            this.Controls.Add(mainPanel);
        }

        /// <summary>
        /// Creates quick statistics panel
        /// </summary>
        private void CreateQuickStatsPanel(Panel parent)
        {
            var statsPanel = new Panel
            {
                Size = new Size(600, 150),
                Location = new Point(50, 270),
                BackColor = Color.FromArgb(245, 245, 245),
                BorderStyle = BorderStyle.FixedSingle
            };

            var titleLabel = new Label
            {
                Text = "إحصائيات سريعة - اليوم",
                Font = new Font(this.Font.FontFamily, 12, FontStyle.Bold),
                Location = new Point(10, 10),
                Size = new Size(580, 25),
                TextAlign = ContentAlignment.MiddleCenter
            };

            lblTodayStats = new Label
            {
                Text = "جاري تحميل الإحصائيات...",
                Location = new Point(10, 50),
                Size = new Size(580, 80),
                TextAlign = ContentAlignment.MiddleCenter,
                Font = new Font(this.Font.FontFamily, 10)
            };

            statsPanel.Controls.AddRange(new Control[] { titleLabel, lblTodayStats });
            parent.Controls.Add(statsPanel);

            // Load today's statistics
            LoadTodayStatistics();
        }

        /// <summary>
        /// Sets up event handlers
        /// </summary>
        private void SetupEventHandlers()
        {
            this.Load += MainForm_Load;
            this.FormClosing += MainForm_FormClosing;
            
            // Session events
            SessionManager.OnSessionExpired += () =>
            {
                this.Invoke(() =>
                {
                    MessageBox.Show("انتهت صلاحية الجلسة. سيتم تسجيل الخروج.", "انتهاء الجلسة",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning,
                        MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
                    
                    ReturnToLogin();
                });
            };
        }

        /// <summary>
        /// Form load event handler
        /// </summary>
        private void MainForm_Load(object? sender, EventArgs e)
        {
            // Update activity
            SessionManager.UpdateActivity();
            
            // Show welcome message
            ShowWelcomeMessage();
        }

        /// <summary>
        /// Form closing event handler
        /// </summary>
        private void MainForm_FormClosing(object? sender, FormClosingEventArgs e)
        {
            if (e.CloseReason == CloseReason.UserClosing)
            {
                var result = MessageBox.Show("هل تريد إغلاق البرنامج؟", "تأكيد الإغلاق",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question,
                    MessageBoxDefaultButton.Button2, MessageBoxOptions.RtlReading);

                if (result == DialogResult.No)
                {
                    e.Cancel = true;
                    return;
                }
            }

            // Cleanup
            _sessionTimer?.Stop();
            _clockTimer?.Stop();
            SessionManager.Logout();
        }

        /// <summary>
        /// Session timer tick event handler
        /// </summary>
        private void SessionTimer_Tick(object? sender, EventArgs e)
        {
            if (SessionManager.IsAuthenticated)
            {
                var sessionInfo = SessionManager.GetSessionInfo();
                lblSessionInfo.Text = $"الجلسة: {sessionInfo.TimeUntilExpiryArabic}";
                
                // Update activity on user interaction
                SessionManager.UpdateActivity();
            }
        }

        /// <summary>
        /// Clock timer tick event handler
        /// </summary>
        private void ClockTimer_Tick(object? sender, EventArgs e)
        {
            lblClock.Text = DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss");
        }

        /// <summary>
        /// Shows welcome message
        /// </summary>
        private void ShowWelcomeMessage()
        {
            var message = $"مرحباً {SessionManager.CurrentUsername}!\n\n" +
                         $"تم تسجيل دخولك بنجاح كـ {SessionManager.CurrentUserRoleArabic}.\n" +
                         "يمكنك الآن استخدام جميع ميزات النظام.";

            MessageBox.Show(message, "مرحباً", MessageBoxButtons.OK, MessageBoxIcon.Information,
                MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
        }

        /// <summary>
        /// Updates user information display
        /// </summary>
        private void UpdateUserInfo()
        {
            if (lblUserInfo != null)
            {
                lblUserInfo.Text = $"المستخدم: {SessionManager.CurrentUsername} ({SessionManager.CurrentUserRoleArabic})";
            }
        }

        /// <summary>
        /// Loads today's statistics
        /// </summary>
        private async void LoadTodayStatistics()
        {
            try
            {
                var today = DateTime.Today;
                var summary = await _salesManager.GetDailySalesSummaryAsync(today);

                var statsText = $"عدد المعاملات: {summary.TotalTransactions}\n" +
                               $"إجمالي المبيعات: {summary.TotalRevenueFormatted}\n" +
                               $"متوسط المعاملة: {summary.AverageTransactionFormatted}\n" +
                               $"عدد العملاء: {summary.UniqueCustomers}";

                lblTodayStats.Text = statsText;
            }
            catch (Exception ex)
            {
                lblTodayStats.Text = $"خطأ في تحميل الإحصائيات: {ex.Message}";
            }
        }

        /// <summary>
        /// Returns to login form
        /// </summary>
        private void ReturnToLogin()
        {
            this.Hide();
            var loginForm = new LoginForm();
            loginForm.Show();
        }

        #region Event Handlers

        private void AddSale_Click(object? sender, EventArgs e)
        {
            var saleForm = new SaleForm();
            if (saleForm.ShowDialog() == DialogResult.OK)
            {
                LoadTodayStatistics(); // Refresh stats
            }
        }

        private void ViewSales_Click(object? sender, EventArgs e)
        {
            var salesListForm = new SalesListForm();
            salesListForm.ShowDialog();
        }

        private void ExportSales_Click(object? sender, EventArgs e)
        {
            // TODO: Implement export functionality
            MessageBox.Show("سيتم تنفيذ وظيفة التصدير قريباً", "قيد التطوير",
                MessageBoxButtons.OK, MessageBoxIcon.Information,
                MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
        }

        private void PrintReport_Click(object? sender, EventArgs e)
        {
            // TODO: Implement print functionality
            MessageBox.Show("سيتم تنفيذ وظيفة الطباعة قريباً", "قيد التطوير",
                MessageBoxButtons.OK, MessageBoxIcon.Information,
                MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
        }

        private void DailyReport_Click(object? sender, EventArgs e)
        {
            var reportForm = new DailyReportForm();
            reportForm.ShowDialog();
        }

        private void MonthlyReport_Click(object? sender, EventArgs e)
        {
            // TODO: Implement monthly report
            MessageBox.Show("سيتم تنفيذ التقرير الشهري قريباً", "قيد التطوير",
                MessageBoxButtons.OK, MessageBoxIcon.Information,
                MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
        }

        private void ProductReport_Click(object? sender, EventArgs e)
        {
            // TODO: Implement product report
            MessageBox.Show("سيتم تنفيذ تقرير المنتجات قريباً", "قيد التطوير",
                MessageBoxButtons.OK, MessageBoxIcon.Information,
                MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
        }

        private void ManageUsers_Click(object? sender, EventArgs e)
        {
            if (!SessionManager.IsAdmin)
            {
                MessageBox.Show("ليس لديك صلاحية لإدارة المستخدمين", "غير مصرح",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
                return;
            }

            var userManagementForm = new UserManagementForm();
            userManagementForm.ShowDialog();
        }

        private void ChangePassword_Click(object? sender, EventArgs e)
        {
            var changePasswordForm = new ChangePasswordForm();
            changePasswordForm.ShowDialog();
        }

        private void CreateBackup_Click(object? sender, EventArgs e)
        {
            if (!SessionManager.IsAdmin)
            {
                MessageBox.Show("ليس لديك صلاحية لإنشاء نسخة احتياطية", "غير مصرح",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
                return;
            }

            var backupForm = new BackupForm();
            backupForm.ShowDialog();
        }

        private void SystemSettings_Click(object? sender, EventArgs e)
        {
            // TODO: Implement system settings
            MessageBox.Show("سيتم تنفيذ إعدادات النظام قريباً", "قيد التطوير",
                MessageBoxButtons.OK, MessageBoxIcon.Information,
                MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
        }

        private void About_Click(object? sender, EventArgs e)
        {
            var aboutMessage = "نظام إدارة متجر الهواتف المحمولة\n" +
                              "الإصدار 1.0.0\n\n" +
                              "تم تطويره باستخدام .NET 6 و Windows Forms\n" +
                              "مع دعم كامل للغة العربية\n\n" +
                              "© 2024 جميع الحقوق محفوظة";

            MessageBox.Show(aboutMessage, "حول البرنامج",
                MessageBoxButtons.OK, MessageBoxIcon.Information,
                MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
        }

        private void UserGuide_Click(object? sender, EventArgs e)
        {
            // TODO: Implement user guide
            MessageBox.Show("سيتم توفير دليل المستخدم قريباً", "قيد التطوير",
                MessageBoxButtons.OK, MessageBoxIcon.Information,
                MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
        }

        private void Logout_Click(object? sender, EventArgs e)
        {
            var result = MessageBox.Show("هل تريد تسجيل الخروج؟", "تأكيد تسجيل الخروج",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question,
                MessageBoxDefaultButton.Button2, MessageBoxOptions.RtlReading);

            if (result == DialogResult.Yes)
            {
                ReturnToLogin();
            }
        }

        private void Exit_Click(object? sender, EventArgs e)
        {
            this.Close();
        }

        #endregion

        #region Form Controls
        private StatusStrip statusStrip = null!;
        private ToolStripStatusLabel lblUserInfo = null!;
        private ToolStripStatusLabel lblClock = null!;
        private ToolStripStatusLabel lblSessionInfo = null!;
        private Label lblTodayStats = null!;
        #endregion
    }
}
