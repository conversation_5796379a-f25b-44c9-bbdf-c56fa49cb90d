using ClosedXML.Excel;
using MobileShopManagement.Models;
using System.Configuration;

namespace MobileShopManagement.Utilities
{
    /// <summary>
    /// مصدر Excel - Excel exporter utility
    /// Handles exporting data to Excel files with Arabic support
    /// </summary>
    public static class ExcelExporter
    {
        /// <summary>
        /// Exports sales data to Excel file
        /// </summary>
        /// <param name="sales">List of sales to export</param>
        /// <param name="filePath">Output file path</param>
        /// <param name="includeCharts">Whether to include charts</param>
        /// <returns>True if export successful</returns>
        public static bool ExportSalesToExcel(List<Sale> sales, string filePath, bool includeCharts = true)
        {
            try
            {
                using var workbook = new XLWorkbook();
                
                // Create main sales worksheet
                var worksheet = workbook.Worksheets.Add("المبيعات");
                
                // Set RTL direction
                worksheet.RightToLeft = true;
                
                // Set up headers
                SetupSalesHeaders(worksheet);
                
                // Add data
                AddSalesData(worksheet, sales);
                
                // Format worksheet
                FormatSalesWorksheet(worksheet, sales.Count);
                
                // Add summary worksheet
                AddSalesSummaryWorksheet(workbook, sales);
                
                // Add charts if requested
                if (includeCharts)
                {
                    AddSalesCharts(workbook, sales);
                }
                
                // Save workbook
                workbook.SaveAs(filePath);
                return true;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"فشل في تصدير البيانات إلى Excel: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Sets up headers for sales worksheet
        /// </summary>
        private static void SetupSalesHeaders(IXLWorksheet worksheet)
        {
            var headers = new[]
            {
                "رقم البيع",
                "اسم المنتج", 
                "سعر البيع",
                "الكمية",
                "المبلغ الإجمالي",
                "اسم العميل",
                "رقم الهاتف",
                "تاريخ البيع",
                "يوم البيع",
                "البائع",
                "ملاحظات"
            };

            for (int i = 0; i < headers.Length; i++)
            {
                var cell = worksheet.Cell(1, i + 1);
                cell.Value = headers[i];
                cell.Style.Font.Bold = true;
                cell.Style.Font.FontSize = 12;
                cell.Style.Font.FontName = "Tahoma";
                cell.Style.Fill.BackgroundColor = XLColor.FromArgb(63, 81, 181);
                cell.Style.Font.FontColor = XLColor.White;
                cell.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
                cell.Style.Alignment.Vertical = XLAlignmentVerticalValues.Center;
                cell.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
            }
        }

        /// <summary>
        /// Adds sales data to worksheet
        /// </summary>
        private static void AddSalesData(IXLWorksheet worksheet, List<Sale> sales)
        {
            for (int i = 0; i < sales.Count; i++)
            {
                var sale = sales[i];
                var row = i + 2; // Start from row 2 (after headers)

                worksheet.Cell(row, 1).Value = sale.SaleID;
                worksheet.Cell(row, 2).Value = sale.ProductName;
                worksheet.Cell(row, 3).Value = sale.SalePrice;
                worksheet.Cell(row, 4).Value = sale.QuantitySold;
                worksheet.Cell(row, 5).Value = sale.TotalAmount;
                worksheet.Cell(row, 6).Value = sale.CustomerName ?? "";
                worksheet.Cell(row, 7).Value = sale.CustomerPhone ?? "";
                worksheet.Cell(row, 8).Value = sale.SaleDate;
                worksheet.Cell(row, 9).Value = sale.SaleDayArabic;
                worksheet.Cell(row, 10).Value = sale.CreatedBy;
                worksheet.Cell(row, 11).Value = sale.Notes ?? "";

                // Format cells
                worksheet.Cell(row, 3).Style.NumberFormat.Format = "#,##0.00 \"ر.س\"";
                worksheet.Cell(row, 5).Style.NumberFormat.Format = "#,##0.00 \"ر.س\"";
                worksheet.Cell(row, 8).Style.NumberFormat.Format = "yyyy/mm/dd hh:mm";
                
                // Set alignment
                for (int col = 1; col <= 11; col++)
                {
                    var cell = worksheet.Cell(row, col);
                    cell.Style.Alignment.Horizontal = col switch
                    {
                        1 or 4 => XLAlignmentHorizontalValues.Center, // ID and Quantity
                        3 or 5 => XLAlignmentHorizontalValues.Center, // Prices
                        8 => XLAlignmentHorizontalValues.Center, // Date
                        _ => XLAlignmentVerticalValues.Center == XLAlignmentVerticalValues.Center ? 
                             XLAlignmentHorizontalValues.Right : XLAlignmentHorizontalValues.Right
                    };
                    cell.Style.Font.FontName = "Tahoma";
                    cell.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                }
            }
        }

        /// <summary>
        /// Formats the sales worksheet
        /// </summary>
        private static void FormatSalesWorksheet(IXLWorksheet worksheet, int dataRows)
        {
            // Auto-fit columns
            worksheet.Columns().AdjustToContents();
            
            // Set minimum column widths
            worksheet.Column(1).Width = 10; // ID
            worksheet.Column(2).Width = 25; // Product Name
            worksheet.Column(3).Width = 15; // Price
            worksheet.Column(4).Width = 10; // Quantity
            worksheet.Column(5).Width = 18; // Total
            worksheet.Column(6).Width = 20; // Customer Name
            worksheet.Column(7).Width = 15; // Phone
            worksheet.Column(8).Width = 18; // Date
            worksheet.Column(9).Width = 12; // Day
            worksheet.Column(10).Width = 15; // Created By
            worksheet.Column(11).Width = 25; // Notes

            // Add alternating row colors
            for (int row = 2; row <= dataRows + 1; row++)
            {
                if (row % 2 == 0)
                {
                    worksheet.Range(row, 1, row, 11).Style.Fill.BackgroundColor = XLColor.FromArgb(248, 248, 248);
                }
            }

            // Add totals row
            if (dataRows > 0)
            {
                var totalRow = dataRows + 3;
                worksheet.Cell(totalRow, 4).Value = "الإجمالي:";
                worksheet.Cell(totalRow, 4).Style.Font.Bold = true;
                worksheet.Cell(totalRow, 4).Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Right;
                
                worksheet.Cell(totalRow, 5).FormulaA1 = $"SUM(E2:E{dataRows + 1})";
                worksheet.Cell(totalRow, 5).Style.Font.Bold = true;
                worksheet.Cell(totalRow, 5).Style.NumberFormat.Format = "#,##0.00 \"ر.س\"";
                worksheet.Cell(totalRow, 5).Style.Fill.BackgroundColor = XLColor.FromArgb(76, 175, 80);
                worksheet.Cell(totalRow, 5).Style.Font.FontColor = XLColor.White;
            }

            // Freeze header row
            worksheet.SheetView.FreezeRows(1);
        }

        /// <summary>
        /// Adds sales summary worksheet
        /// </summary>
        private static void AddSalesSummaryWorksheet(XLWorkbook workbook, List<Sale> sales)
        {
            var summarySheet = workbook.Worksheets.Add("الملخص");
            summarySheet.RightToLeft = true;

            // Title
            summarySheet.Cell(1, 1).Value = "ملخص المبيعات";
            summarySheet.Cell(1, 1).Style.Font.Bold = true;
            summarySheet.Cell(1, 1).Style.Font.FontSize = 16;
            summarySheet.Cell(1, 1).Style.Font.FontName = "Tahoma";
            summarySheet.Range(1, 1, 1, 4).Merge();
            summarySheet.Cell(1, 1).Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;

            // Summary data
            var totalSales = sales.Sum(s => s.TotalAmount);
            var totalTransactions = sales.Count;
            var averageTransaction = totalTransactions > 0 ? totalSales / totalTransactions : 0;
            var uniqueCustomers = sales.Where(s => !string.IsNullOrEmpty(s.CustomerName))
                                      .Select(s => s.CustomerName)
                                      .Distinct()
                                      .Count();

            var summaryData = new[]
            {
                new { Label = "إجمالي المبيعات:", Value = $"{totalSales:N2} ر.س" },
                new { Label = "عدد المعاملات:", Value = totalTransactions.ToString() },
                new { Label = "متوسط المعاملة:", Value = $"{averageTransaction:N2} ر.س" },
                new { Label = "عدد العملاء:", Value = uniqueCustomers.ToString() }
            };

            for (int i = 0; i < summaryData.Length; i++)
            {
                var row = i + 3;
                summarySheet.Cell(row, 1).Value = summaryData[i].Label;
                summarySheet.Cell(row, 1).Style.Font.Bold = true;
                summarySheet.Cell(row, 1).Style.Font.FontName = "Tahoma";
                
                summarySheet.Cell(row, 2).Value = summaryData[i].Value;
                summarySheet.Cell(row, 2).Style.Font.FontName = "Tahoma";
                summarySheet.Cell(row, 2).Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
            }

            // Top products
            var topProducts = sales.GroupBy(s => s.ProductName)
                                  .Select(g => new
                                  {
                                      ProductName = g.Key,
                                      TotalQuantity = g.Sum(s => s.QuantitySold),
                                      TotalRevenue = g.Sum(s => s.TotalAmount),
                                      TransactionCount = g.Count()
                                  })
                                  .OrderByDescending(p => p.TotalRevenue)
                                  .Take(10)
                                  .ToList();

            if (topProducts.Any())
            {
                summarySheet.Cell(8, 1).Value = "أفضل المنتجات مبيعاً";
                summarySheet.Cell(8, 1).Style.Font.Bold = true;
                summarySheet.Cell(8, 1).Style.Font.FontSize = 14;
                summarySheet.Cell(8, 1).Style.Font.FontName = "Tahoma";

                // Headers
                var productHeaders = new[] { "اسم المنتج", "الكمية", "المبيعات", "المعاملات" };
                for (int i = 0; i < productHeaders.Length; i++)
                {
                    var cell = summarySheet.Cell(10, i + 1);
                    cell.Value = productHeaders[i];
                    cell.Style.Font.Bold = true;
                    cell.Style.Font.FontName = "Tahoma";
                    cell.Style.Fill.BackgroundColor = XLColor.FromArgb(33, 150, 243);
                    cell.Style.Font.FontColor = XLColor.White;
                    cell.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
                }

                // Data
                for (int i = 0; i < topProducts.Count; i++)
                {
                    var row = i + 11;
                    var product = topProducts[i];
                    
                    summarySheet.Cell(row, 1).Value = product.ProductName;
                    summarySheet.Cell(row, 2).Value = product.TotalQuantity;
                    summarySheet.Cell(row, 3).Value = $"{product.TotalRevenue:N2} ر.س";
                    summarySheet.Cell(row, 4).Value = product.TransactionCount;

                    for (int col = 1; col <= 4; col++)
                    {
                        summarySheet.Cell(row, col).Style.Font.FontName = "Tahoma";
                        summarySheet.Cell(row, col).Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                    }
                }
            }

            summarySheet.Columns().AdjustToContents();
        }

        /// <summary>
        /// Adds charts to the workbook
        /// </summary>
        private static void AddSalesCharts(XLWorkbook workbook, List<Sale> sales)
        {
            var chartSheet = workbook.Worksheets.Add("الرسوم البيانية");
            chartSheet.RightToLeft = true;

            // Daily sales chart data
            var dailySales = sales.GroupBy(s => s.SaleDate.Date)
                                 .Select(g => new
                                 {
                                     Date = g.Key,
                                     TotalSales = g.Sum(s => s.TotalAmount),
                                     TransactionCount = g.Count()
                                 })
                                 .OrderBy(d => d.Date)
                                 .ToList();

            if (dailySales.Any())
            {
                chartSheet.Cell(1, 1).Value = "المبيعات اليومية";
                chartSheet.Cell(1, 1).Style.Font.Bold = true;
                chartSheet.Cell(1, 1).Style.Font.FontSize = 14;

                chartSheet.Cell(3, 1).Value = "التاريخ";
                chartSheet.Cell(3, 2).Value = "المبيعات";
                chartSheet.Cell(3, 3).Value = "المعاملات";

                for (int i = 0; i < dailySales.Count; i++)
                {
                    var row = i + 4;
                    chartSheet.Cell(row, 1).Value = dailySales[i].Date;
                    chartSheet.Cell(row, 2).Value = dailySales[i].TotalSales;
                    chartSheet.Cell(row, 3).Value = dailySales[i].TransactionCount;
                }

                // Format chart data
                chartSheet.Range(3, 1, 3 + dailySales.Count, 3).Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                chartSheet.Columns().AdjustToContents();
            }
        }

        /// <summary>
        /// Exports daily report to Excel
        /// </summary>
        /// <param name="summary">Daily sales summary</param>
        /// <param name="filePath">Output file path</param>
        /// <returns>True if export successful</returns>
        public static bool ExportDailyReportToExcel(DailySalesSummary summary, string filePath)
        {
            try
            {
                using var workbook = new XLWorkbook();
                var worksheet = workbook.Worksheets.Add($"تقرير يومي - {summary.DateArabic}");
                worksheet.RightToLeft = true;

                // Title
                worksheet.Cell(1, 1).Value = $"التقرير اليومي - {summary.DateArabic}";
                worksheet.Cell(1, 1).Style.Font.Bold = true;
                worksheet.Cell(1, 1).Style.Font.FontSize = 16;
                worksheet.Cell(1, 1).Style.Font.FontName = "Tahoma";
                worksheet.Range(1, 1, 1, 4).Merge();
                worksheet.Cell(1, 1).Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;

                // Summary section
                var summaryData = new[]
                {
                    new { Label = "عدد المعاملات:", Value = summary.TotalTransactions.ToString() },
                    new { Label = "إجمالي المبيعات:", Value = summary.TotalRevenueFormatted },
                    new { Label = "متوسط المعاملة:", Value = summary.AverageTransactionFormatted },
                    new { Label = "عدد العملاء:", Value = summary.UniqueCustomers.ToString() }
                };

                for (int i = 0; i < summaryData.Length; i++)
                {
                    var row = i + 3;
                    worksheet.Cell(row, 1).Value = summaryData[i].Label;
                    worksheet.Cell(row, 1).Style.Font.Bold = true;
                    worksheet.Cell(row, 1).Style.Font.FontName = "Tahoma";
                    
                    worksheet.Cell(row, 2).Value = summaryData[i].Value;
                    worksheet.Cell(row, 2).Style.Font.FontName = "Tahoma";
                }

                // Top products section
                if (summary.TopProducts.Any())
                {
                    worksheet.Cell(8, 1).Value = "أفضل المنتجات مبيعاً";
                    worksheet.Cell(8, 1).Style.Font.Bold = true;
                    worksheet.Cell(8, 1).Style.Font.FontSize = 14;
                    worksheet.Cell(8, 1).Style.Font.FontName = "Tahoma";

                    var headers = new[] { "اسم المنتج", "الكمية", "المبيعات", "المعاملات" };
                    for (int i = 0; i < headers.Length; i++)
                    {
                        var cell = worksheet.Cell(10, i + 1);
                        cell.Value = headers[i];
                        cell.Style.Font.Bold = true;
                        cell.Style.Font.FontName = "Tahoma";
                        cell.Style.Fill.BackgroundColor = XLColor.FromArgb(63, 81, 181);
                        cell.Style.Font.FontColor = XLColor.White;
                        cell.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
                    }

                    for (int i = 0; i < summary.TopProducts.Count; i++)
                    {
                        var row = i + 11;
                        var product = summary.TopProducts[i];
                        
                        worksheet.Cell(row, 1).Value = product.ProductName;
                        worksheet.Cell(row, 2).Value = product.TotalQuantity;
                        worksheet.Cell(row, 3).Value = product.TotalRevenueFormatted;
                        worksheet.Cell(row, 4).Value = product.TransactionCount;

                        for (int col = 1; col <= 4; col++)
                        {
                            worksheet.Cell(row, col).Style.Font.FontName = "Tahoma";
                            worksheet.Cell(row, col).Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                        }
                    }
                }

                worksheet.Columns().AdjustToContents();
                workbook.SaveAs(filePath);
                return true;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"فشل في تصدير التقرير إلى Excel: {ex.Message}", ex);
            }
        }
    }
}
