-- Sample Data for Mobile Shop Management System
-- This script inserts initial data for testing and demonstration

USE MobileShopDB;
GO

-- Insert default admin user (password: admin123)
-- BCrypt hash for 'admin123' with salt
IF NOT EXISTS (SELECT 1 FROM Users WHERE Username = 'admin')
BEGIN
    INSERT INTO Users (Username, PasswordHash, Salt, Role, IsActive, CreatedBy)
    VALUES ('admin', 
            '$2a$11$K2QZ8jQjQjQjQjQjQjQjQeK2QZ8jQjQjQjQjQjQjQjQjQjQjQjQjQj', 
            '$2a$11$K2QZ8jQjQjQjQjQjQjQjQe', 
            'Admin', 
            1, 
            'SYSTEM');
    PRINT 'Default admin user created - تم إنشاء مستخدم المدير الافتراضي';
END
GO

-- Insert sample cashier user (password: cashier123)
IF NOT EXISTS (SELECT 1 FROM Users WHERE Username = 'cashier')
BEGIN
    INSERT INTO Users (Username, PasswordHash, Salt, Role, IsActive, CreatedBy)
    VALUES ('cashier', 
            '$2a$11$L3RZ9kRkRkRkRkRkRkRkReL3RZ9kRkRkRkRkRkRkRkRkRkRkRkRkRk', 
            '$2a$11$L3RZ9kRkRkRkRkRkRkRkRe', 
            'Cashier', 
            1, 
            'admin');
    PRINT 'Sample cashier user created - تم إنشاء مستخدم الكاشير النموذجي';
END
GO

-- Insert system settings
IF NOT EXISTS (SELECT 1 FROM SystemSettings WHERE SettingKey = 'ShopName')
BEGIN
    INSERT INTO SystemSettings (SettingKey, SettingValue, Description, Category, IsUserConfigurable)
    VALUES 
    ('ShopName', 'متجر الهواتف المحمولة الذكية', 'اسم المتجر', 'General', 1),
    ('ShopAddress', 'شارع الملك فهد، الرياض، المملكة العربية السعودية', 'عنوان المتجر', 'General', 1),
    ('ShopPhone', '+966-11-1234567', 'رقم هاتف المتجر', 'General', 1),
    ('ShopEmail', '<EMAIL>', 'البريد الإلكتروني للمتجر', 'General', 1),
    ('TaxRate', '15', 'معدل الضريبة المضافة (%)', 'Financial', 1),
    ('Currency', 'ريال سعودي', 'العملة المستخدمة', 'Financial', 1),
    ('CurrencySymbol', 'ر.س', 'رمز العملة', 'Financial', 1),
    ('ReceiptHeader', 'مرحباً بكم في متجر الهواتف المحمولة', 'رأس الفاتورة', 'Printing', 1),
    ('ReceiptFooter', 'شكراً لزيارتكم - نتطلع لخدمتكم مرة أخرى', 'تذييل الفاتورة', 'Printing', 1),
    ('BackupRetentionDays', '30', 'عدد أيام الاحتفاظ بالنسخ الاحتياطية', 'System', 1);
    
    PRINT 'System settings inserted - تم إدراج إعدادات النظام';
END
GO

-- Insert sample sales data for demonstration
IF NOT EXISTS (SELECT 1 FROM Sales WHERE ProductName = 'iPhone 15 Pro')
BEGIN
    INSERT INTO Sales (ProductName, SalePrice, SaleDate, QuantitySold, CustomerName, CustomerPhone, Notes, CreatedBy)
    VALUES 
    ('iPhone 15 Pro', 4500.00, DATEADD(day, -5, GETDATE()), 1, 'أحمد محمد علي', '0501234567', 'بيع نقدي', 'admin'),
    ('Samsung Galaxy S24', 3200.00, DATEADD(day, -4, GETDATE()), 1, 'فاطمة أحمد', '0509876543', 'دفع بالتقسيط', 'cashier'),
    ('iPhone 14', 3800.00, DATEADD(day, -3, GETDATE()), 2, 'محمد عبدالله', '0512345678', 'خصم 5%', 'admin'),
    ('Xiaomi 13 Pro', 2100.00, DATEADD(day, -3, GETDATE()), 1, 'نورا سالم', '0598765432', 'بيع نقدي', 'cashier'),
    ('OnePlus 11', 2800.00, DATEADD(day, -2, GETDATE()), 1, 'خالد الأحمد', '0587654321', 'ضمان إضافي', 'admin'),
    ('Google Pixel 8', 2400.00, DATEADD(day, -2, GETDATE()), 1, 'سارة محمد', '0576543210', 'بيع نقدي', 'cashier'),
    ('iPhone 13', 3200.00, DATEADD(day, -1, GETDATE()), 1, 'عبدالرحمن علي', '0565432109', 'تبديل جهاز قديم', 'admin'),
    ('Samsung Galaxy A54', 1800.00, DATEADD(day, -1, GETDATE()), 3, 'مريم أحمد', '0554321098', 'طلبية خاصة', 'cashier'),
    ('Huawei P60 Pro', 2600.00, GETDATE(), 1, 'يوسف الخالد', '0543210987', 'بيع نقدي', 'admin'),
    ('Nothing Phone 2', 2200.00, GETDATE(), 1, 'ليلى سعد', '0532109876', 'ضمان ممتد', 'cashier');
    
    PRINT 'Sample sales data inserted - تم إدراج بيانات المبيعات النموذجية';
END
GO

-- Insert sample audit log entries
INSERT INTO AuditLog (TableName, RecordID, Action, NewValues, ChangedBy, ChangedDate)
VALUES 
('Users', 1, 'INSERT', 'Username: admin, Role: Admin', 'SYSTEM', GETDATE()),
('Users', 2, 'INSERT', 'Username: cashier, Role: Cashier', 'admin', GETDATE()),
('SystemSettings', 1, 'INSERT', 'Initial system configuration', 'SYSTEM', GETDATE());

PRINT 'Sample audit log entries inserted - تم إدراج سجلات المراجعة النموذجية';
GO

-- Create views for reporting
CREATE OR ALTER VIEW vw_SalesReport AS
SELECT 
    s.SaleID,
    s.ProductName AS [اسم المنتج],
    s.SalePrice AS [سعر البيع],
    s.QuantitySold AS [الكمية],
    (s.SalePrice * s.QuantitySold) AS [المبلغ الإجمالي],
    s.SaleDate AS [تاريخ البيع],
    s.SaleDay AS [يوم البيع],
    s.CustomerName AS [اسم العميل],
    s.CustomerPhone AS [رقم الهاتف],
    s.CreatedBy AS [البائع],
    u.Role AS [دور البائع]
FROM Sales s
INNER JOIN Users u ON s.CreatedBy = u.Username
WHERE s.IsDeleted = 0;
GO

CREATE OR ALTER VIEW vw_MonthlySalesSummary AS
SELECT 
    YEAR(SaleDate) AS [السنة],
    MONTH(SaleDate) AS [الشهر],
    DATENAME(MONTH, SaleDate) AS [اسم الشهر],
    COUNT(*) AS [عدد المعاملات],
    SUM(SalePrice * QuantitySold) AS [إجمالي المبيعات],
    AVG(SalePrice * QuantitySold) AS [متوسط المعاملة],
    COUNT(DISTINCT CustomerName) AS [عدد العملاء المختلفين],
    COUNT(DISTINCT ProductName) AS [عدد المنتجات المختلفة]
FROM Sales
WHERE IsDeleted = 0
GROUP BY YEAR(SaleDate), MONTH(SaleDate), DATENAME(MONTH, SaleDate);
GO

PRINT 'Sample data insertion completed successfully - تم إكمال إدراج البيانات النموذجية بنجاح';
PRINT 'Default login credentials:';
PRINT 'Admin - Username: admin, Password: admin123';
PRINT 'Cashier - Username: cashier, Password: cashier123';
GO
