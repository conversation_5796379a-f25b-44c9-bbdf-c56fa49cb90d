using Microsoft.Extensions.Logging;
using System.Runtime.CompilerServices;
using System.Text.Json;

namespace MobileShopManagement.Services;

/// <summary>
/// خدمة التسجيل المحدثة لـ .NET 9 - Modern logging service for .NET 9
/// Provides structured logging with Arabic support and performance optimizations
/// </summary>
public sealed class LoggingService
{
    private readonly ILogger<LoggingService> _logger;
    private readonly ConfigurationService _config;

    public LoggingService(ILogger<LoggingService> logger, ConfigurationService config)
    {
        _logger = logger;
        _config = config;
    }

    /// <summary>
    /// Logs user authentication events
    /// </summary>
    public void LogAuthentication(string username, bool success, string? ipAddress = null, 
        [CallerMemberName] string? callerName = null)
    {
        using var scope = _logger.BeginScope(new Dictionary<string, object>
        {
            ["EventType"] = "Authentication",
            ["Username"] = username,
            ["Success"] = success,
            ["IPAddress"] = ipAddress ?? "Unknown",
            ["Caller"] = callerName ?? "Unknown"
        });

        if (success)
        {
            _logger.LogInformation("User {Username} authenticated successfully from {IPAddress}", 
                username, ipAddress ?? "Unknown");
        }
        else
        {
            _logger.LogWarning("Failed authentication attempt for user {Username} from {IPAddress}", 
                username, ipAddress ?? "Unknown");
        }
    }

    /// <summary>
    /// Logs sales operations with structured data
    /// </summary>
    public void LogSalesOperation(string operation, int? saleId, string productName, decimal amount, 
        string username, [CallerMemberName] string? callerName = null)
    {
        using var scope = _logger.BeginScope(new Dictionary<string, object>
        {
            ["EventType"] = "SalesOperation",
            ["Operation"] = operation,
            ["SaleId"] = saleId ?? 0,
            ["ProductName"] = productName,
            ["Amount"] = amount,
            ["Username"] = username,
            ["Caller"] = callerName ?? "Unknown"
        });

        _logger.LogInformation("Sales operation {Operation} performed by {Username} for product {ProductName} (Amount: {Amount:C})",
            operation, username, productName, amount);
    }

    /// <summary>
    /// Logs database operations with performance metrics
    /// </summary>
    public void LogDatabaseOperation(string operation, string tableName, TimeSpan duration, 
        bool success, string? errorMessage = null, [CallerMemberName] string? callerName = null)
    {
        using var scope = _logger.BeginScope(new Dictionary<string, object>
        {
            ["EventType"] = "DatabaseOperation",
            ["Operation"] = operation,
            ["TableName"] = tableName,
            ["Duration"] = duration.TotalMilliseconds,
            ["Success"] = success,
            ["Caller"] = callerName ?? "Unknown"
        });

        if (success)
        {
            _logger.LogInformation("Database operation {Operation} on {TableName} completed in {Duration}ms",
                operation, tableName, duration.TotalMilliseconds);
        }
        else
        {
            _logger.LogError("Database operation {Operation} on {TableName} failed after {Duration}ms: {ErrorMessage}",
                operation, tableName, duration.TotalMilliseconds, errorMessage ?? "Unknown error");
        }
    }

    /// <summary>
    /// Logs security events with high priority
    /// </summary>
    public void LogSecurityEvent(string eventType, string username, string description, 
        string? ipAddress = null, [CallerMemberName] string? callerName = null)
    {
        using var scope = _logger.BeginScope(new Dictionary<string, object>
        {
            ["EventType"] = "SecurityEvent",
            ["SecurityEventType"] = eventType,
            ["Username"] = username,
            ["Description"] = description,
            ["IPAddress"] = ipAddress ?? "Unknown",
            ["Caller"] = callerName ?? "Unknown"
        });

        _logger.LogWarning("Security event {EventType} for user {Username}: {Description} from {IPAddress}",
            eventType, username, description, ipAddress ?? "Unknown");
    }

    /// <summary>
    /// Logs system errors with full context
    /// </summary>
    public void LogSystemError(Exception exception, string context, 
        Dictionary<string, object>? additionalData = null, [CallerMemberName] string? callerName = null)
    {
        var scopeData = new Dictionary<string, object>
        {
            ["EventType"] = "SystemError",
            ["Context"] = context,
            ["ExceptionType"] = exception.GetType().Name,
            ["Caller"] = callerName ?? "Unknown"
        };

        if (additionalData != null)
        {
            foreach (var kvp in additionalData)
            {
                scopeData[kvp.Key] = kvp.Value;
            }
        }

        using var scope = _logger.BeginScope(scopeData);

        _logger.LogError(exception, "System error in {Context}: {ErrorMessage}", context, exception.Message);
    }

    /// <summary>
    /// Logs performance metrics
    /// </summary>
    public void LogPerformanceMetric(string operationName, TimeSpan duration, 
        Dictionary<string, object>? metrics = null, [CallerMemberName] string? callerName = null)
    {
        var scopeData = new Dictionary<string, object>
        {
            ["EventType"] = "PerformanceMetric",
            ["OperationName"] = operationName,
            ["Duration"] = duration.TotalMilliseconds,
            ["Caller"] = callerName ?? "Unknown"
        };

        if (metrics != null)
        {
            foreach (var kvp in metrics)
            {
                scopeData[kvp.Key] = kvp.Value;
            }
        }

        using var scope = _logger.BeginScope(scopeData);

        var logLevel = duration.TotalMilliseconds switch
        {
            > 5000 => LogLevel.Warning,  // > 5 seconds
            > 1000 => LogLevel.Information, // > 1 second
            _ => LogLevel.Debug
        };

        _logger.Log(logLevel, "Operation {OperationName} completed in {Duration}ms", 
            operationName, duration.TotalMilliseconds);
    }

    /// <summary>
    /// Logs user activity for audit purposes
    /// </summary>
    public void LogUserActivity(string username, string activity, string details, 
        [CallerMemberName] string? callerName = null)
    {
        using var scope = _logger.BeginScope(new Dictionary<string, object>
        {
            ["EventType"] = "UserActivity",
            ["Username"] = username,
            ["Activity"] = activity,
            ["Details"] = details,
            ["Caller"] = callerName ?? "Unknown"
        });

        _logger.LogInformation("User {Username} performed activity {Activity}: {Details}", 
            username, activity, details);
    }

    /// <summary>
    /// Logs configuration changes
    /// </summary>
    public void LogConfigurationChange(string settingName, object? oldValue, object? newValue, 
        string username, [CallerMemberName] string? callerName = null)
    {
        using var scope = _logger.BeginScope(new Dictionary<string, object>
        {
            ["EventType"] = "ConfigurationChange",
            ["SettingName"] = settingName,
            ["OldValue"] = oldValue?.ToString() ?? "null",
            ["NewValue"] = newValue?.ToString() ?? "null",
            ["Username"] = username,
            ["Caller"] = callerName ?? "Unknown"
        });

        _logger.LogInformation("Configuration setting {SettingName} changed by {Username} from {OldValue} to {NewValue}",
            settingName, username, oldValue?.ToString() ?? "null", newValue?.ToString() ?? "null");
    }

    /// <summary>
    /// Creates a performance measurement scope
    /// </summary>
    public IDisposable BeginPerformanceScope(string operationName, 
        [CallerMemberName] string? callerName = null)
    {
        return new PerformanceScope(this, operationName, callerName);
    }

    /// <summary>
    /// Performance measurement scope for automatic timing
    /// </summary>
    private sealed class PerformanceScope : IDisposable
    {
        private readonly LoggingService _loggingService;
        private readonly string _operationName;
        private readonly string? _callerName;
        private readonly DateTime _startTime;
        private bool _disposed;

        public PerformanceScope(LoggingService loggingService, string operationName, string? callerName)
        {
            _loggingService = loggingService;
            _operationName = operationName;
            _callerName = callerName;
            _startTime = DateTime.UtcNow;
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                var duration = DateTime.UtcNow - _startTime;
                _loggingService.LogPerformanceMetric(_operationName, duration, callerName: _callerName);
                _disposed = true;
            }
        }
    }
}

/// <summary>
/// Extension methods for logging service
/// </summary>
public static class LoggingExtensions
{
    /// <summary>
    /// Logs an operation with automatic performance measurement
    /// </summary>
    public static async Task<T> LogOperationAsync<T>(this LoggingService loggingService, 
        string operationName, Func<Task<T>> operation, [CallerMemberName] string? callerName = null)
    {
        using var scope = loggingService.BeginPerformanceScope(operationName, callerName);
        try
        {
            var result = await operation();
            return result;
        }
        catch (Exception ex)
        {
            loggingService.LogSystemError(ex, operationName, callerName: callerName);
            throw;
        }
    }

    /// <summary>
    /// Logs an operation with automatic performance measurement (synchronous)
    /// </summary>
    public static T LogOperation<T>(this LoggingService loggingService, 
        string operationName, Func<T> operation, [CallerMemberName] string? callerName = null)
    {
        using var scope = loggingService.BeginPerformanceScope(operationName, callerName);
        try
        {
            var result = operation();
            return result;
        }
        catch (Exception ex)
        {
            loggingService.LogSystemError(ex, operationName, callerName: callerName);
            throw;
        }
    }
}
